

<div class="container mt-4">
  <h2 class="mb-3">تفاصيل العميل</h2>

  <div class="card mb-4">
    <div class="card-body">
      <h5 class="card-title"><%= customer.name %></h5>
      <p class="card-text"><strong>رقم الهاتف:</strong> <%= customer.phoneNumber %></p>
      <p class="card-text"><strong>البريد الإلكتروني:</strong> <%= customer.email || 'غير متوفر' %></p>
      <p class="card-text"><strong>تاريخ الإنشاء:</strong> <%= customer.createdAt.toLocaleDateString() %></p>
    </div>
  </div>

  <h4 class="mb-3">الطلبات المرتبطة</h4>

  <% if (customer.orders.length === 0) { %>
    <div class="alert alert-info">لا توجد طلبات مسجلة لهذا العميل.</div>
  <% } else { %>
    <table class="table table-bordered table-striped">
      <thead>
        <tr>
          <th>#</th>
          <th>المجموع الكلي</th>
          <th>الحالة</th>
          <th>تاريخ الطلب</th>
          <th>عرض</th>
        </tr>
      </thead>
      <tbody>
        <% customer.orders.forEach((order, index) => { %>
          <tr>
            <td><%= order.id %></td>
            <td><%= order.totalPrice %> ل.س</td>
            <td>
              <%
                let badgeClass = 'secondary';
                let statusText = 'غير معروف';

                switch (order.status) {
                    case 'pending':
                    badgeClass = 'warning';
                    statusText = 'في الانتظار';
                    break;
                    case 'processing':
                    badgeClass = 'info';
                    statusText = 'جاري التحضير';
                    break;
                    case 'rejected':
                    badgeClass = 'danger';
                    statusText = 'مرفوض';
                    break;
                    case 'out_for_delivery':
                    badgeClass = 'primary';
                    statusText = 'في الطريق';
                    break;
                    case 'completed':
                    badgeClass = 'success';
                    statusText = 'تم التسليم';
                    break;
                    case 'cancelled':
                    badgeClass = 'danger';
                    statusText = 'ملغي';
                    break;
                }
                %>
                
              <span class="badge bg-<%= badgeClass %>"><%= statusText %></span>
            </td>
            <td><%= order.createdAt.toLocaleDateString() %></td>
            <td>
              <a href="/admin/orders/<%= order.id %>" class="btn btn-sm btn-outline-primary">تفاصيل</a>
            </td>
          </tr>
        <% }) %>
      </tbody>
    </table>
  <% } %>

  <a href="/admin/customers" class="btn btn-secondary mt-3">رجوع إلى قائمة العملاء</a>
</div>


