importScripts('https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.22.0/firebase-messaging-compat.js');

// Firebase configuration - ensure consistency across all files
const firebaseConfig = {
  apiKey: "AIzaSyBdtHd9YCMgqvBs0KAVaen3Jg4M6x2mdhE",
  authDomain: "company-firebase-77daf.firebaseapp.com",
  projectId: "company-firebase-77daf",
  storageBucket: "company-firebase-77daf.appspot.com", // Fixed: consistent with dashboard.ejs
  messagingSenderId: "76415949840",
  appId: "1:76415949840:web:d74b24b187a1abf392fe95",
  measurementId: "G-YRPEE91G0C"
};

firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

// Enhanced background message handling with error handling
messaging.onBackgroundMessage(function(payload) {
  console.log('[firebase-messaging-sw.js] Received background message:', payload);

  try {
    const notificationTitle = payload.notification?.title || 'إشعار جديد';
    const notificationOptions = {
      body: payload.notification?.body || 'لديك إشعار جديد',
      icon: '/logo.png',
      badge: '/logo.png',
      tag: payload.data?.tag || 'default',
      requireInteraction: false,
      silent: false,
      data: payload.data || {},
      actions: payload.data?.actions ? JSON.parse(payload.data.actions) : []
    };

    // Show notification
    return self.registration.showNotification(notificationTitle, notificationOptions);
  } catch (error) {
    console.error('[firebase-messaging-sw.js] Error showing notification:', error);

    // Fallback notification
    return self.registration.showNotification('إشعار جديد', {
      body: 'لديك إشعار جديد',
      icon: '/logo.png'
    });
  }
});

// Handle notification click events
self.addEventListener('notificationclick', function(event) {
  console.log('[firebase-messaging-sw.js] Notification click received.');

  event.notification.close();

  // Handle notification click action
  const clickAction = event.notification.data?.clickAction || '/admin/dashboard';

  event.waitUntil(
    clients.openWindow(clickAction)
  );
});

// Handle notification close events
self.addEventListener('notificationclose', function(event) {
  console.log('[firebase-messaging-sw.js] Notification closed:', event.notification.tag);
});