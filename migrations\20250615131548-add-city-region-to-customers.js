'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('Customers', 'city', {
      type: Sequelize.STRING(100),
      allowNull: true,
      comment: 'المدينة'
    });

    await queryInterface.addColumn('Customers', 'region', {
      type: Sequelize.STRING(100),
      allowNull: true,
      comment: 'المنطقة'
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('Customers', 'city');
    await queryInterface.removeColumn('Customers', 'region');
  }
};
