<div class="container mt-4">
    <!-- نموذج تحكم في عرض السائقين -->
  <form method="GET" class="mb-3">
    <div class="form-check">
      <label class="form-check-label fw-bold" for="showAll">
        منطقة الزبون   :   <%= pendingOrders.length > 0 ? ' (' + pendingOrders[0].customer.city + ')' : '' %>
      </label>
      <br>
      <input
        class="form-check-input"
        type="checkbox"
        name="showAll"
        id="showAll"
        value="on"
        <%= showAll ? 'checked' : '' %>>
      <label class="form-check-label fw-bold" for="showAll">
        عرض جميع السائقين
      </label>
    </div>
  </form>

  <form action="/admin/deliveries/<%= delivery.id %>" method="POST" class="card p-4 shadow-sm bg-white">
    <h4 class="mb-4 text-center text-primary">تعديل التوصيل</h4>

    <div class="mb-3">
      <label for="deliveryPersonId" class="form-label fw-bold">سائق التوصيل</label>
      <select id="deliveryPersonId" name="deliveryPersonId" class="form-select" required>
        <option value="">اختر السائق</option>
        <% deliveryPeople.forEach(person => { %>
          <option value="<%= person.id %>" <%= delivery.deliveryPersonId == person.id ? 'selected' : '' %>>
            <%= person.name %>
          </option>
        <% }); %>
      </select>
    </div>

    <div class="mb-3">
      <label for="status" class="form-label fw-bold">الحالة</label>
      <select id="status" name="status" class="form-select" required>
        <option value="in_transit" <%= delivery.status === 'in_transit' ? 'selected' : '' %>>قيد التوصيل</option>
        <option value="delivered" <%= delivery.status === 'delivered' ? 'selected' : '' %>>تم التوصيل</option>
        <option value="cancelled" <%= delivery.status === 'cancelled' ? 'selected' : '' %>>إلغاء</option>
      </select>
    </div>

    <div class="mb-3">
      <label for="pickupTime" class="form-label fw-bold">وقت الاستلام</label>
      <input 
        type="datetime-local" 
        id="pickupTime" 
        name="pickupTime" 
        class="form-control" 
        value="<%= delivery.pickupTime ? delivery.pickupTime.toISOString().slice(0,16) : '' %>"
      >
    </div>

    <div class="mb-3">
      <label for="deliveryTime" class="form-label fw-bold">وقت التوصيل</label>
      <input 
        type="datetime-local" 
        id="deliveryTime" 
        name="deliveryTime" 
        class="form-control" 
        value="<%= delivery.deliveryTime ? delivery.deliveryTime.toISOString().slice(0,16) : '' %>"
      >
    </div>

    <div class="mb-3">
      <label for="notes" class="form-label fw-bold">ملاحظات</label>
      <textarea id="notes" name="notes" class="form-control" rows="3"><%= delivery.notes || '' %></textarea>
    </div>

    <div class="d-flex justify-content-between align-items-center mt-4">
      <a href="/admin/deliveries" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left"></i> العودة
      </a>
      <button type="submit" class="btn btn-success">
        <i class="fas fa-save"></i> تحديث التوصيل
      </button>
    </div>
  </form>
</div>
<script>
 document.addEventListener('DOMContentLoaded', () => {
  const checkbox = document.getElementById('showAll');
  if (checkbox) {
    checkbox.addEventListener('change', () => {
      const params = new URLSearchParams(window.location.search);
      if (checkbox.checked) {
        params.set('showAll', 'on');
      } else {
        params.delete('showAll');
      }
      window.location.search = params.toString();
    });
  }
});

</script>