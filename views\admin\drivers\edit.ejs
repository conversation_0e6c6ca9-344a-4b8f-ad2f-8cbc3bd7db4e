<form action="/admin/drivers/<%= deliveryPerson.id %>" method="POST" enctype="multipart/form-data" class="card p-4 shadow-sm bg-white">
  <h4 class="mb-4 text-center text-primary">تعديل بيانات السائق</h4>

  <div class="mb-3">
    <label for="name" class="form-label fw-bold">الاسم الكامل</label>
    <input type="text" name="name" id="name" class="form-control" required value="<%= deliveryPerson.name %>">
  </div>
  <div class="col-md-6">
    <div class="mb-3">
        <label for="username" class="form-label">اسم المستخدم *</label>
        <input type="text" class="form-control" id="username" name="username"
                value="<%= deliveryPerson.username %>"
                required placeholder="أدخل اسم المستخدم">
    </div>
  </div>
  <div class="col-md-6">
    <div class="mb-3">
        <label for="password" class="form-label">كلمة المرور *</label>
        <input type="password" class="form-control" id="password" name="password"
        value="<%= deliveryPerson.password %>"
                required placeholder="أدخل كلمة المرور">
    </div>
  </div>
  <div class="mb-3">
    <label for="phoneNumber" class="form-label fw-bold">رقم الهاتف</label>
    <input type="tel" name="phoneNumber" id="phoneNumber" class="form-control" required value="<%= deliveryPerson.phoneNumber %>">
  </div>

  <div class="mb-3">
    <label class="form-label fw-bold">صورة السائق الحالية</label><br>
    <% if (deliveryPerson.image) { %>
      <img src="<%= deliveryPerson.image %>" alt="صورة السائق" width="100">
    <% } else { %>
      <span class="text-muted">لا توجد صورة</span>
    <% } %>
  </div>

  <div class="mb-3">
    <label for="image" class="form-label fw-bold">تغيير الصورة <span class="text-muted">(اختياري)</span></label>
    <input type="file" name="image" id="image" class="form-control" accept="image/*">
  </div>

  <div class="mb-3">
    <button type="button" class="btn btn-outline-primary" id="selectFromMap">
      <i class="fas fa-map-marker-alt"></i> أضف منطقة من الخريطة
    </button>
  </div>

  <!-- خريطة -->
  <div class="mb-3" id="mapContainer" style="display: none;">
    <div class="card">
      <div class="card-header d-flex justify-content-between">
        <span>اختر موقع منطقة</span>
        <button type="button" class="btn btn-sm btn-secondary" id="closeMap">إغلاق</button>
      </div>
      <div class="card-body">
        <div id="map" style="height: 400px; width: 100%;"></div>
        <div class="mt-3 d-flex gap-2">
          <button type="button" class="btn btn-success" id="confirmLocation">إضافة هذه المنطقة</button>
          <button type="button" class="btn btn-secondary" id="getCurrentLocation">موقعي الحالي</button>
        </div>
        <div id="loading" class="mt-2 text-primary" style="display:none;">🔄 جاري جلب العنوان...</div>
      </div>
    </div>
  </div>

  <!-- جدول المناطق -->
  <div class="mt-4">
    <h5>المناطق التي يغطيها السائق</h5>
    <table class="table table-bordered" id="regionsTable">
      <thead>
        <tr>
          <th>المدينة</th>
          <th>المنطقة</th>
          <th>العنوان الكامل</th>
          <th>إزالة</th>
        </tr>
      </thead>
      <tbody>
        <% deliveryPerson.areas.forEach((area, i) => { %>
          <tr>
            <td><%= area.city %></td>
            <td><%= area.region %></td>
            <td><%= area.address %></td>
            <td><button type="button" class="btn btn-sm btn-danger remove-region">حذف</button></td>
          </tr>
          <input type="hidden" name="regions[<%= i %>][city]" value="<%= area.city %>">
          <input type="hidden" name="regions[<%= i %>][region]" value="<%= area.region %>">
          <input type="hidden" name="regions[<%= i %>][address]" value="<%= area.address %>">
          <input type="hidden" name="regions[<%= i %>][latitude]" value="<%= area.latitude %>">
          <input type="hidden" name="regions[<%= i %>][longitude]" value="<%= area.longitude %>">
        <% }) %>
      </tbody>
    </table>
  </div>

  <!-- الحقول المخفية -->
  <div id="regionsHiddenFields"></div>

  <input type="hidden" id="latitude">
  <input type="hidden" id="longitude">
  <input type="hidden" id="address">
  <input type="hidden" id="city">
  <input type="hidden" id="region">

  <div class="d-flex justify-content-between align-items-center mt-4">
    <a href="/admin/deliveryPersons" class="btn btn-outline-secondary">
      <i class="fas fa-arrow-left"></i> إلغاء
    </a>
    <button type="submit" class="btn btn-primary">
      <i class="fas fa-save"></i> حفظ التغييرات
    </button>
  </div>
</form>

<!-- Leaflet -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<!-- JS الخريطة والمعالجة نفس الإضافة مع regionCounter يبدأ من آخر index موجود -->
<script>
  let map, marker, selectedLocation = null;
  let regionCounter = <%= deliveryPerson.areas.length %>;

document.addEventListener('DOMContentLoaded', function() {
  document.getElementById('selectFromMap').addEventListener('click', function() {
    document.getElementById('mapContainer').style.display = 'block';
    setTimeout(initMap, 100);
  });

  document.getElementById('closeMap').addEventListener('click', function() {
    document.getElementById('mapContainer').style.display = 'none';
  });

  document.getElementById('confirmLocation').addEventListener('click', function() {
    if (!selectedLocation) return alert('يرجى اختيار موقع أولاً');

    const city = document.getElementById('city').value;
    const region = document.getElementById('region').value;
    const address = document.getElementById('address').value;
    const lat = selectedLocation.lat;
    const lng = selectedLocation.lng;

    const exists = Array.from(document.querySelectorAll('input[name^="regions["]'))
      .some(input => input.value.includes(lat) && input.value.includes(lng));
    if (exists) return alert('تمت إضافة هذه المنطقة مسبقاً.');

    // جدول
    const table = document.querySelector('#regionsTable tbody');
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${city}</td>
      <td>${region}</td>
      <td>${address}</td>
      <td><button type="button" class="btn btn-sm btn-danger remove-region">حذف</button></td>
    `;
    table.appendChild(row);

    // الحقول المخفية
    const hiddenFields = document.getElementById('regionsHiddenFields');
    hiddenFields.insertAdjacentHTML('beforeend', `
      <input type="hidden" name="regions[${regionCounter}][city]" value="${city}">
      <input type="hidden" name="regions[${regionCounter}][region]" value="${region}">
      <input type="hidden" name="regions[${regionCounter}][address]" value="${address}">
      <input type="hidden" name="regions[${regionCounter}][latitude]" value="${lat}">
      <input type="hidden" name="regions[${regionCounter}][longitude]" value="${lng}">
    `);
    regionCounter++;

    document.getElementById('mapContainer').style.display = 'none';
  });

  document.addEventListener('click', function(e) {
    if (e.target.classList.contains('remove-region')) {
      const row = e.target.closest('tr');
      const index = Array.from(row.parentNode.children).indexOf(row);
      row.remove();
      const hidden = document.querySelectorAll(`#regionsHiddenFields input[name^="regions[${index}]"]`);
      hidden.forEach(el => el.remove());
    }
  });

  document.getElementById('getCurrentLocation').addEventListener('click', function() {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(function(pos) {
        const lat = pos.coords.latitude;
        const lng = pos.coords.longitude;
        selectedLocation = { lat, lng };
        map.setView([lat, lng], 15);
        marker.setLatLng([lat, lng]);
        updateLocationFields(lat, lng);
      }, () => alert('تعذر تحديد موقعك الحالي'));
    } else {
      alert('المتصفح لا يدعم تحديد الموقع');
    }
  });
});

function initMap() {
  const defaultLocation = [24.7136, 46.6753]; // الرياض
  map = L.map('map').setView(defaultLocation, 10);
  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);
  marker = L.marker(defaultLocation, { draggable: true }).addTo(map);
  selectedLocation = { lat: defaultLocation[0], lng: defaultLocation[1] };
  updateLocationFields(defaultLocation[0], defaultLocation[1]);

  map.on('click', function(e) {
    selectedLocation = { lat: e.latlng.lat, lng: e.latlng.lng };
    marker.setLatLng([selectedLocation.lat, selectedLocation.lng]);
    updateLocationFields(selectedLocation.lat, selectedLocation.lng);
  });

  marker.on('dragend', function(e) {
    const pos = e.target.getLatLng();
    selectedLocation = { lat: pos.lat, lng: pos.lng };
    updateLocationFields(pos.lat, pos.lng);
  });
}

function updateLocationFields(lat, lng) {
  document.getElementById('latitude').value = lat;
  document.getElementById('longitude').value = lng;
  fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&accept-language=ar`)
    .then(res => res.json())
    .then(data => {
      if (data?.display_name) {
        document.getElementById('address').value = data.display_name;
        const addr = data.address;
        document.getElementById('city').value = addr.city || addr.town || addr.village || addr.county || '';
        document.getElementById('region').value = addr.state || addr.region || addr.province || '';
      }
      document.getElementById('loading').style.display = 'none';
    })
    .catch(err => {
      console.error('Geocoding failed:', err);
      document.getElementById('loading').style.display = 'none';
    });

  document.getElementById('loading').style.display = 'block';
}
</script>