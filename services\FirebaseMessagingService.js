const admin = require('firebase-admin');
const logger = require('../utils/logger');
const { AdminToken, CustomerToken,DriverToken} = require('../models');
const {
  notificationSettings,
  invalidTokenErrorCodes,
  retrySettings
} = require('../config/firebase');
const { Console } = require('winston/lib/winston/transports');

class FirebaseMessagingService {
  /**
   * إرسال إشعار لتوكن واحد
   */
  static async sendToToken(token, notification, data = {}) {
    try {
      const message = this.buildMessage(notification, data, token);
      const response = await admin.messaging().send(message);
      
      logger.info(`Notification sent successfully to token: ${token.substring(0, 20)}...`);
      return { success: true, response, token };
      
    } catch (error) {
      logger.error(`Failed to send notification to token ${token.substring(0, 20)}...:`, error.message);
      
      // التحقق من صحة التوكن
      if (invalidTokenErrorCodes.includes(error.code)) {
        await this.handleInvalidToken(token);
      }
      
      throw error;
    }
  }

  /**
   * إرسال إشعار لعدة توكنات
   */
  static async sendToMultipleTokens(tokens, notification, data = {}) {
    
    if (!tokens || tokens.length === 0) {
      console.log('No tokens provided for notification');
      logger.warn('No tokens provided for notification');
      return { successCount: 0, failureCount: 0, results: [] };
    }

    const promises = tokens.map(token => 
      this.sendToTokenWithRetry(token, notification, data)
    );

    const results = await Promise.allSettled(promises);
    
    const successCount = results.filter(r => 
      r.status === 'fulfilled' && r.value.success
    ).length;
    
    const failureCount = results.length - successCount;
     console.log(`Notification ${successCount} successful, ${failureCount} failed`);
    logger.info(`Notification batch results: ${successCount} successful, ${failureCount} failed`);
    
    return {
      successCount,
      failureCount,
      results: results.map(r => r.status === 'fulfilled' ? r.value : { success: false, error: r.reason })
    };
  }

  /**
   * إرسال إشعار لجميع المديرين
   */
  static async sendToAllAdmins(notification, data = {}) {
    try {
      const adminTokens = await AdminToken.findAll();

      if (adminTokens.length === 0) {
        logger.warn('No admin tokens found for notification');
        return { successCount: 0, failureCount: 0, results: [] };
      }

      const tokens = adminTokens.map(adminToken => adminToken.token);
      return await this.sendToMultipleTokens(tokens, notification, data);

    } catch (error) {
      logger.error('Error sending notification to all admins:', error);
      throw error;
    }
  }

  /**
   * إرسال إشعار لعميل محدد
   */
  static async sendToCustomer(customerId, notification, data = {}) {
    try {
      const customerTokens = await CustomerToken.findAll({
        where: {
          customerId: customerId,
          isActive: true
        }
      });

      if (customerTokens.length === 0) {
        console.log(`No active tokens found for customer ${customerId}`);
        logger.warn(`No active tokens found for customer ${customerId}`);
        return { successCount: 0, failureCount: 0, results: [] };
      }

      const tokens = customerTokens.map(customerToken => customerToken.token);
      // تحديث آخر استخدام للتوكنات
      await CustomerToken.update(
        { lastUsed: new Date() },
        {
          where: {
            customerId: customerId,
            isActive: true
          }
        }
      );

      return await this.sendToMultipleTokens(tokens, notification, data);

    } catch (error) {
      console.log(`Error sending notification to customer ${customerId}:`, error);
      logger.error(`Error sending notification to customer ${customerId}:`, error);
      throw error;
    }
  }

  /**
   * إرسال إشعار لجميع العملاء النشطين
   */
  static async sendToAllCustomers(notification, data = {}) {
    try {
      const customerTokens = await CustomerToken.findAll({
        where: { isActive: true }
      });

      if (customerTokens.length === 0) {
        logger.warn('No active customer tokens found for notification');
        return { successCount: 0, failureCount: 0, results: [] };
      }

      const tokens = customerTokens.map(customerToken => customerToken.token);

      // تحديث آخر استخدام للتوكنات
      await CustomerToken.update(
        { lastUsed: new Date() },
        { where: { isActive: true } }
      );

      return await this.sendToMultipleTokens(tokens, notification, data);

    } catch (error) {
      logger.error('Error sending notification to all customers:', error);
      throw error;
    }
  }

    /**
   * إرسال إشعار للسائق محدد
   */
  static async sendToDriver(driverId, notification, data = {}) {
    try {
      const driverTokens = await DriverToken.findAll({
        where: {
          driverId: driverId,
          isActive: true
        }
      });

      if (driverTokens.length === 0) {
        console.log(`No active tokens found for driver ${driverId}`);
        logger.warn(`No active tokens found for driver ${driverId}`);
        return { successCount: 0, failureCount: 0, results: [] };
      }

      const tokens = driverTokens.map(dt => dt.token);

      // تحديث آخر استخدام للتوكنات
      await DriverToken.update(
        { lastUsed: new Date() },
        {
          where: {
            driverId: driverId,
            isActive: true
          }
        }
      );

      return await this.sendToMultipleTokens(tokens, notification, data);

    } catch (error) {
      console.log(`Error sending notification to driver ${driverId}:`, error);
      logger.error(`Error sending notification to driver ${driverId}:`, error);
      throw error;
    }
  }


  /**
   * إرسال إشعار مع إعادة المحاولة
   */
  static async sendToTokenWithRetry(token, notification, data = {}, retryCount = 0) {
    try {
      return await this.sendToToken(token, notification, data);
    } catch (error) {
      if (retryCount < retrySettings.maxRetries && !invalidTokenErrorCodes.includes(error.code)) {
        const delay = retrySettings.retryDelay * Math.pow(retrySettings.backoffMultiplier, retryCount);
        console.log(`Retrying notification after ${delay}ms (attempt ${retryCount + 1}/${retrySettings.maxRetries})`);
        logger.info(`Retrying notification after ${delay}ms (attempt ${retryCount + 1}/${retrySettings.maxRetries})`);
        
        await new Promise(resolve => setTimeout(resolve, delay));
        return await this.sendToTokenWithRetry(token, notification, data, retryCount + 1);
      }
      
      return { success: false, error: error.message, token };
    }
  }

  /**
   * بناء رسالة الإشعار
   */
  static buildMessage(notification, data = {}, token) {
    const message = {
      notification: {
        title: notification.title,
        body: notification.body
      },
      data: {
        ...data,
        timestamp: Date.now().toString()
      },
      token,
      android: notificationSettings.android,
      apns: notificationSettings.apns,
      webpush: {
        ...notificationSettings.webpush,
        notification: {
          ...notificationSettings.webpush.notification,
          ...notificationSettings.defaultOptions
        }
      }
    };

    return message;
  }

  /**
   * معالجة التوكنات غير الصالحة
   */
  static async handleInvalidToken(token) {
    try {
      // البحث في توكنات المديرين
      const adminToken = await AdminToken.findOne({ where: { token } });
      if (adminToken) {
        await adminToken.destroy();
        logger.info(`Deleted invalid admin token: ${token.substring(0, 20)}...`);
        return;
      }

      // البحث في توكنات العملاء
      const customerToken = await CustomerToken.findOne({ where: { token } });
      if (customerToken) {
        await customerToken.update({ isActive: false });
        logger.info(`Deactivated invalid customer token: ${token.substring(0, 20)}...`);
        return;
      }

      logger.warn(`Token not found in any table: ${token.substring(0, 20)}...`);
    } catch (error) {
      logger.error('Error handling invalid token:', error);
    }
  }

  /**
   * التحقق من صحة التوكن
   */
  static async validateToken(token) {
    try {
      const message = {
        data: { test: 'true' },
        token,
        dryRun: true // لا يرسل الإشعار فعلياً
      };
      
      await admin.messaging().send(message);
      return true;
    } catch (error) {
      if (invalidTokenErrorCodes.includes(error.code)) {
        await this.handleInvalidToken(token);
        return false;
      }
      
      // خطأ آخر غير متعلق بصحة التوكن
      logger.warn(`Token validation failed with non-token error: ${error.message}`);
      return true; // نعتبر التوكن صالح في هذه الحالة
    }
  }

  /**
   * تنظيف التوكنات غير الصالحة
   */
  static async cleanupInvalidTokens() {
    try {
      let totalInvalidCount = 0;

      // تنظيف توكنات المديرين
      const adminTokens = await AdminToken.findAll();
      const adminValidationPromises = adminTokens.map(async (adminToken) => {
        const isValid = await this.validateToken(adminToken.token);
        return { token: adminToken, isValid, type: 'admin' };
      });

      // تنظيف توكنات العملاء
      const customerTokens = await CustomerToken.findAll({ where: { isActive: true } });
      const customerValidationPromises = customerTokens.map(async (customerToken) => {
        const isValid = await this.validateToken(customerToken.token);
        return { token: customerToken, isValid, type: 'customer' };
      });

      const allResults = await Promise.allSettled([
        ...adminValidationPromises,
        ...customerValidationPromises
      ]);

      const invalidAdminCount = allResults.filter(r =>
        r.status === 'fulfilled' && !r.value.isValid && r.value.type === 'admin'
      ).length;

      const invalidCustomerCount = allResults.filter(r =>
        r.status === 'fulfilled' && !r.value.isValid && r.value.type === 'customer'
      ).length;

      totalInvalidCount = invalidAdminCount + invalidCustomerCount;

      logger.info(`Token cleanup completed. Removed ${invalidAdminCount} invalid admin tokens and ${invalidCustomerCount} invalid customer tokens.`);
      return totalInvalidCount;

    } catch (error) {
      logger.error('Error during token cleanup:', error);
      throw error;
    }
  }
}

module.exports = FirebaseMessagingService;
