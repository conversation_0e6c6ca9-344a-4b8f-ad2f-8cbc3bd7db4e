<div class="container mt-4">
    <h2>Delivery for Order #<%= order.id %></h2>
    <p><strong>Customer:</strong> <%= order.customer.name %></p>
    <hr>
    <% if (!delivery) { %>
      <form action="/admin/orders/<%= order.id %>/delivery" method="POST">
        <button class="btn btn-primary">Mark as In Delivery</button>
      </form>
    <% } else { %>
      <p><strong>Status:</strong> <%= delivery.status %></p>
      <form action="/admin/deliveries/<%= delivery.id %>/status" method="POST" class="mb-3">
        <select name="status" class="form-select w-auto d-inline-block">
          <% ['pending','in_transit','delivered','cancelled'].forEach(s => { %>
            <option value="<%= s %>" <%= delivery.status===s?'selected':'' %>><%= s.replace('_',' ') %></option>
          <% }) %>
        </select>
        <button class="btn btn-success">Update Status</button>
      </form>
      <p><strong>Picked Up:</strong> <%= delivery.pickupTime ? delivery.pickupTime.toLocaleString() : '-' %></p>
      <p><strong>Delivered:</strong> <%= delivery.deliveryTime ? delivery.deliveryTime.toLocaleString() : '-' %></p>
      <% if (delivery.notes) { %>
        <p><strong>Notes:</strong> <%= delivery.notes %></p>
      <% } %>
    <% } %>
    <a href="/admin/deliveries" class="btn btn-secondary mt-3">Back to Deliveries</a>
  </div>
  