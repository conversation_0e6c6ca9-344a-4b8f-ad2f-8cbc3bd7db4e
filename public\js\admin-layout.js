/**
 * Admin Layout JavaScript - Professional JavaScript Organization
 * Handles general layout functionality and UI interactions
 */

/**
 * Initialize layout functionality when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeSidebarToggle();
    initializeAlertAutoHide();
    initializeDropdownToggles();
    console.log('✅ تم تهيئة تخطيط الإدارة بنجاح');
});

/**
 * Initialize sidebar toggle functionality
 */
function initializeSidebarToggle() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('adminSidebar');

    if (sidebarToggle && sidebar) {
        // Sidebar toggle functionality
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            if (window.innerWidth <= 1024 && 
                !sidebar.contains(event.target) && 
                !sidebarToggle.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        });

        console.log('✅ تم تهيئة وظائف الشريط الجانبي');
    }
}

/**
 * Initialize auto-hide functionality for alerts
 */
function initializeAlertAutoHide() {
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            if (alert.classList.contains('show')) {
                alert.classList.remove('show');
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 300);
            }
        });
    }, 5000);

    console.log('✅ تم تهيئة إخفاء التنبيهات التلقائي');
}

/**
 * Initialize dropdown toggle functionality
 */
function initializeDropdownToggles() {
    const toggleButtons = document.querySelectorAll('.toggle-btn');
    
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const nextElement = this.nextElementSibling;
            if (nextElement) {
                nextElement.classList.toggle('show');
            }
        });
    });

    if (toggleButtons.length > 0) {
        console.log(`✅ تم تهيئة ${toggleButtons.length} أزرار تبديل`);
    }
}

/**
 * Show a toast notification
 * @param {string} message - The message to display
 * @param {string} type - The type of notification (success, error, warning, info)
 * @param {number} duration - Duration in milliseconds (default: 5000)
 */
function showToast(message, type = 'info', duration = 5000) {
    const toast = document.createElement('div');
    const alertType = type === 'error' ? 'danger' : type;
    
    toast.className = `alert alert-${alertType} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-${getToastIcon(type)} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto-remove after specified duration
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, duration);
}

/**
 * Get appropriate icon for toast type
 * @param {string} type - The toast type
 * @returns {string} - The FontAwesome icon class
 */
function getToastIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * Show loading spinner
 * @param {string} containerId - ID of the container to show spinner in
 */
function showLoadingSpinner(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = `
            <div class="d-flex justify-content-center align-items-center p-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <span class="ms-2">جاري التحميل...</span>
            </div>
        `;
    }
}

/**
 * Hide loading spinner and restore content
 * @param {string} containerId - ID of the container
 * @param {string} content - Content to restore
 */
function hideLoadingSpinner(containerId, content = '') {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = content;
    }
}

/**
 * Confirm action with user
 * @param {string} message - Confirmation message
 * @param {Function} callback - Function to execute if confirmed
 */
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * Format date for display
 * @param {string|Date} date - Date to format
 * @returns {string} - Formatted date string
 */
function formatDate(date) {
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * Format time ago
 * @param {string|Date} date - Date to format
 * @returns {string} - Time ago string
 */
function formatTimeAgo(date) {
    const now = new Date();
    const dateObj = new Date(date);
    const diff = now - dateObj;
    
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (seconds < 60) return 'الآن';
    if (minutes < 60) return `${minutes} دقيقة`;
    if (hours < 24) return `${hours} ساعة`;
    return `${days} يوم`;
}

/**
 * Debounce function to limit function calls
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} - Debounced function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Throttle function to limit function calls
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @returns {Function} - Throttled function
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Handle AJAX errors consistently
 * @param {Error} error - The error object
 * @param {string} defaultMessage - Default error message
 */
function handleAjaxError(error, defaultMessage = 'حدث خطأ غير متوقع') {
    console.error('AJAX Error:', error);
    
    let message = defaultMessage;
    if (error.response && error.response.data && error.response.data.message) {
        message = error.response.data.message;
    } else if (error.message) {
        message = error.message;
    }
    
    showToast(message, 'error');
}

// Export functions for global access
window.showToast = showToast;
window.showLoadingSpinner = showLoadingSpinner;
window.hideLoadingSpinner = hideLoadingSpinner;
window.confirmAction = confirmAction;
window.formatDate = formatDate;
window.formatTimeAgo = formatTimeAgo;
window.debounce = debounce;
window.throttle = throttle;
window.handleAjaxError = handleAjaxError;
