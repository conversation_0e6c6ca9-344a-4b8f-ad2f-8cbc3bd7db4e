/**
 * Notification Actions - Professional JavaScript Organization
 * Handles notification page-specific actions and interactions
 */

/**
 * Mark notification as read
 * @param {number} notificationId - The notification ID
 */
async function markAsRead(notificationId) {
    try {
        const response = await fetch(`/admin/notifications/${notificationId}/read`, { 
            method: 'POST' 
        });
        
        if (response.ok) {
            const element = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (element) {
                element.classList.remove('list-group-item-warning');
                const badge = element.querySelector('.badge');
                if (badge) {
                    badge.remove();
                }
            }
            showToast('تم تحديد الإشعار كمقروء', 'success');
        } else {
            throw new Error('فشل في تحديث الإشعار');
        }
    } catch (error) {
        error('خطأ في تحديد الإشعار كمقروء:', error);
        showToast('حدث خطأ أثناء تحديث الإشعار', 'error');
    }
}

/**
 * Delete notification
 * @param {number} notificationId - The notification ID
 */
async function deleteNotification(notificationId) {
    if (!confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
        return;
    }

    try {
        const response = await fetch(`/admin/notifications/${notificationId}`, { 
            method: 'DELETE' 
        });
        
        if (response.ok) {
            const element = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (element) {
                element.remove();
            }
            showToast('تم حذف الإشعار بنجاح', 'success');
        } else {
            throw new Error('فشل في حذف الإشعار');
        }
    } catch (error) {
        console.error('خطأ في حذف الإشعار:', error);
        showToast('حدث خطأ أثناء حذف الإشعار', 'error');
    }
}

/**
 * Mark all notifications as read
 */
async function markAllAsRead() {
    try {
        const response = await fetch('/admin/notifications/mark-all-read', { 
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                userType: 'admin',
                userId: 1
            })
        });
        
        if (response.ok) {
            // Update UI
            document.querySelectorAll('.list-group-item-warning').forEach(item => {
                item.classList.remove('list-group-item-warning');
                const badge = item.querySelector('.badge');
                if (badge) {
                    badge.remove();
                }
            });
            showToast('تم تحديد جميع الإشعارات كمقروءة', 'success');
        } else {
            throw new Error('فشل في تحديث الإشعارات');
        }
    } catch (error) {
        console.error('خطأ في تحديد جميع الإشعارات كمقروءة:', error);
        showToast('حدث خطأ أثناء تحديث الإشعارات', 'error');
    }
}

/**
 * Clear all notifications
 */
async function clearAllNotifications() {
    if (!confirm('هل أنت متأكد من حذف جميع الإشعارات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }

    try {
        const response = await fetch('/admin/notifications/clear-all', { 
            method: 'DELETE' 
        });
        
        if (response.ok) {
            // Reload the page to show updated list
            location.reload();
        } else {
            throw new Error('فشل في حذف الإشعارات');
        }
    } catch (error) {
        console.error('خطأ في حذف جميع الإشعارات:', error);
        showToast('حدث خطأ أثناء حذف الإشعارات', 'error');
    }
}

/**
 * Show toast notification
 * @param {string} message - The message to display
 * @param {string} type - The type of toast (success, error, info, warning)
 */
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    const alertType = type === 'error' ? 'danger' : type;
    
    toast.className = `alert alert-${alertType} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-${getToastIcon(type)} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 5000);
}

/**
 * Get appropriate icon for toast type
 * @param {string} type - The toast type
 * @returns {string} - The FontAwesome icon class
 */
function getToastIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * Initialize notification page functionality
 */
function initializeNotificationPage() {
    // Add event listeners for bulk actions
    const markAllBtn = document.getElementById('markAllReadBtn');
    if (markAllBtn) {
        markAllBtn.addEventListener('click', markAllAsRead);
    }

    const clearAllBtn = document.getElementById('clearAllBtn');
    if (clearAllBtn) {
        clearAllBtn.addEventListener('click', clearAllNotifications);
    }

    // Add event listeners for individual notification actions
    document.querySelectorAll('[data-action="mark-read"]').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            const notificationId = btn.getAttribute('data-notification-id');
            if (notificationId) {
                markAsRead(parseInt(notificationId));
            }
        });
    });

    document.querySelectorAll('[data-action="delete"]').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            const notificationId = btn.getAttribute('data-notification-id');
            if (notificationId) {
                deleteNotification(parseInt(notificationId));
            }
        });
    });

    console.log('✅ تم تهيئة صفحة الإشعارات بنجاح');
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeNotificationPage);

// Export functions for global access
window.markAsRead = markAsRead;
window.deleteNotification = deleteNotification;
window.markAllAsRead = markAllAsRead;
window.clearAllNotifications = clearAllNotifications;
window.showToast = showToast;
