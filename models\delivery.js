'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Delivery extends Model {
    static associate(models) {
      // سجل التوصيل قد يرتبط بمندوب توصيل
      Delivery.belongsTo(models.DeliveryPerson, {
        foreignKey: 'deliveryPersonId', as: 'courier', onDelete: 'SET NULL'
      });

      // سجل التوصيل مرتبط بطلب واحد
      Delivery.belongsTo(models.Order, {
        foreignKey: 'orderId', as: 'order'
      });
    }
  }

  Delivery.init({
    orderId: DataTypes.INTEGER,
    deliveryPersonId: DataTypes.INTEGER,
    status: DataTypes.ENUM('pending','in_transit','delivered','cancelled'),
    pickupTime: DataTypes.DATE,
    deliveryTime: DataTypes.DATE,
    notes: DataTypes.TEXT
  }, {
    sequelize,
    modelName: 'Delivery',
    tableName: 'Deliveries'
  });

  return Delivery;
};