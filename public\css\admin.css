/**
 * تنسيق خاص بلوحة الإدارة
 */

/* ========== تخطيط الإدارة ========== */
.admin-layout {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

.admin-sidebar {
  width: 280px;
  background: linear-gradient(135deg, var(--bg-dark) 0%, #1e293b 100%);
  color: var(--text-white);
  box-shadow: var(--shadow-xl);
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
  transition: all 0.3s ease;
}

.admin-content {
  flex: 1;
  margin-right: 280px;
  background-color: var(--bg-secondary);
  min-height: 100vh;
}

.admin-header {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

.admin-main {
  padding: var(--spacing-xl);
}

/* ========== شريط جانبي محسن ========== */
.admin-sidebar-header {
  padding: var(--spacing-xl) var(--spacing-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.admin-logo {
  font-size: var(--font-size-2xl);
  font-weight: 800;
  color: var(--text-white);
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.admin-logo i {
  color: var(--primary-light);
}

.admin-nav {
  padding: var(--spacing-lg) 0;
}

.admin-nav-section {
  margin-bottom: var(--spacing-xl);
}

.admin-nav-title {
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.admin-nav-item {
  margin-bottom: var(--spacing-xs);
}

.admin-nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  color: #cbd5e1;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
}

.admin-nav-link:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-white);
  text-decoration: none;
}

.admin-nav-link.active {
  background-color: var(--primary-color);
  color: var(--text-white);
  box-shadow: 0 4px 12px rgba(178, 205, 156, 0.3);
}

.admin-nav-link.active::before {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--primary-light);
}

.admin-nav-icon {
  width: 20px;
  text-align: center;
  font-size: var(--font-size-lg);
}

.admin-nav-text {
  flex: 1;
}

.admin-nav-badge {
  background-color: var(--danger-color);
  color: var(--text-white);
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

/* ========== رأس الصفحة ========== */
.admin-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.admin-header-title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.admin-header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.admin-user-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.admin-user-menu:hover {
  background-color: var(--border-color);
  text-decoration: none;
  color: var(--text-primary);
}

.admin-user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  font-weight: 600;
}

/* ========== بطاقات الإحصائيات ========== */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.stat-card {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, transparent 30%, rgba(37, 99, 235, 0.1) 100%);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.stat-card-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0;
}

.stat-card-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: var(--text-white);
}

.stat-card-icon.primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.stat-card-icon.success {
  background: linear-gradient(135deg, var(--success-color) 0%, #10b981 100%);
}

.stat-card-icon.warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, #f59e0b 100%);
}

.stat-card-icon.danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, #ef4444 100%);
}

.stat-card-value {
  font-size: var(--font-size-4xl);
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1;
}

.stat-card-change {
  font-size: var(--font-size-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.stat-card-change.positive {
  color: var(--success-color);
}

.stat-card-change.negative {
  color: var(--danger-color);
}

.stat-card-change.neutral {
  color: var(--text-muted);
}

/* ========== جداول محسنة ========== */
.admin-table-container {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  margin-bottom: var(--spacing-xl);
}

.admin-table-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-tertiary);
}

.admin-table-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
}

.admin-table th {
  background-color: var(--bg-tertiary);
  padding: var(--spacing-lg);
  text-align: right;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-color);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-table td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  vertical-align: middle;
}

.admin-table tbody tr {
  transition: background-color 0.2s ease;
}

.admin-table tbody tr:hover {
  background-color: var(--bg-secondary);
}

/* ========== أزرار الإجراءات ========== */
.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.btn-action {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  border: none;
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.btn-action:hover {
  transform: translateY(-1px);
  text-decoration: none;
}

.btn-view {
  background-color: var(--info-color);
  color: var(--text-white);
}

.btn-view:hover {
  background-color: #0e7490;
  color: var(--text-white);
}

.btn-edit {
  background-color: var(--warning-color);
  color: var(--text-white);
}

.btn-edit:hover {
  background-color: #b45309;
  color: var(--text-white);
}

.btn-delete {
  background-color: var(--danger-color);
  color: var(--text-white);
}

.btn-delete:hover {
  background-color: #b91c1c;
  color: var(--text-white);
}

/* ========== شارات الحالة ========== */
.status-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.active {
  background-color: var(--success-light);
  color: var(--success-color);
}

.status-badge.inactive {
  background-color: var(--danger-light);
  color: var(--danger-color);
}

.status-badge.pending {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

/* ========== الاستجابة للشاشات الصغيرة ========== */
@media (max-width: 1024px) {
  .admin-sidebar {
    transform: translateX(100%);
    width: 100%;
    max-width: 320px;
  }
  
  .admin-sidebar.show {
    transform: translateX(0);
  }
  
  .admin-content {
    margin-right: 0;
  }
  
  .admin-header {
    padding-right: 60px;
  }
  
  .sidebar-toggle {
    display: block;
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 1001;
    background-color: var(--primary-color);
    color: var(--text-white);
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    cursor: pointer;
    box-shadow: var(--shadow-lg);
  }
}

@media (min-width: 1025px) {
  .sidebar-toggle {
    display: none;
  }
}

@media (max-width: 768px) {
  .admin-main {
    padding: var(--spacing-lg);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .admin-table-container {
    overflow-x: auto;
  }
  
  .admin-table {
    min-width: 600px;
  }
  
  .admin-header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }
}
