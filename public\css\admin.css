/**
 * تنسيق خاص بلوحة الإدارة
 */

/* ========== تخطيط الإدارة ========== */
.admin-layout {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

.admin-sidebar {
  width: 280px;
  background: linear-gradient(135deg, #0c4a6e 0%, #075985 50%, #0369a1 100%);
  color: var(--text-white);
  box-shadow:
    0 20px 40px rgba(14, 165, 233, 0.3),
    0 8px 24px rgba(56, 189, 248, 0.2),
    inset 1px 0 0 rgba(255, 255, 255, 0.1);
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-content {
  flex: 1;
  margin-right: 280px;
  background-color: var(--bg-secondary);
  min-height: 100vh;
}

.admin-header {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

.admin-main {
  padding: var(--spacing-xl);
}

/* ========== شريط جانبي محسن ========== */
.admin-sidebar-header {
  padding: var(--spacing-xl) var(--spacing-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.admin-logo {
  font-size: var(--font-size-2xl);
  font-weight: 800;
  color: var(--text-white);
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.admin-logo i {
  color: var(--primary-light);
}

/* ========== رأس الصفحة ========== */
.admin-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.admin-header-title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.admin-header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.admin-user-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.admin-user-menu:hover {
  background-color: var(--border-color);
  text-decoration: none;
  color: var(--text-primary);
}

.admin-user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  font-weight: 600;
}

/* ========== بطاقات الإحصائيات ========== */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.stat-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(240, 249, 255, 0.9) 100%);
  border: 1px solid rgba(14, 165, 233, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow:
    0 10px 30px rgba(14, 165, 233, 0.15),
    0 4px 20px rgba(56, 189, 248, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-400), var(--primary-600), var(--primary-400));
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

.stat-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(14, 165, 233, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(14, 165, 233, 0.25),
    0 8px 30px rgba(56, 189, 248, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(14, 165, 233, 0.4);
}

.stat-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.stat-card-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0;
}

.stat-card-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: var(--text-white);
}

.stat-card-icon.primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.stat-card-icon.success {
  background: linear-gradient(135deg, var(--success-color) 0%, #10b981 100%);
}

.stat-card-icon.warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, #f59e0b 100%);
}

.stat-card-icon.danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, #ef4444 100%);
}

.stat-card-value {
  font-size: var(--font-size-4xl);
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1;
}

.stat-card-change {
  font-size: var(--font-size-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.stat-card-change.positive {
  color: var(--success-color);
}

.stat-card-change.negative {
  color: var(--danger-color);
}

.stat-card-change.neutral {
  color: var(--text-muted);
}

/* ========== جداول محسنة ========== */
.admin-table-container {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  margin-bottom: var(--spacing-xl);
}

.admin-table-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-tertiary);
}

.admin-table-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
}

.admin-table th {
  background-color: var(--bg-tertiary);
  padding: var(--spacing-lg);
  text-align: right;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-color);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-table td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  vertical-align: middle;
}

.admin-table tbody tr {
  transition: background-color 0.2s ease;
}

.admin-table tbody tr:hover {
  background-color: var(--bg-secondary);
}

/* ========== أزرار الإجراءات ========== */
.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.btn-action {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  border: none;
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.btn-action:hover {
  transform: translateY(-1px);
  text-decoration: none;
}

.btn-view {
  background-color: var(--info-color);
  color: var(--text-white);
}

.btn-view:hover {
  background-color: #0e7490;
  color: var(--text-white);
}

.btn-edit {
  background-color: var(--warning-color);
  color: var(--text-white);
}

.btn-edit:hover {
  background-color: #b45309;
  color: var(--text-white);
}

.btn-delete {
  background-color: var(--danger-color);
  color: var(--text-white);
}

.btn-delete:hover {
  background-color: #b91c1c;
  color: var(--text-white);
}

/* ========== شارات الحالة ========== */
.status-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.active {
  background-color: var(--success-light);
  color: var(--success-color);
}

.status-badge.inactive {
  background-color: var(--danger-light);
  color: var(--danger-color);
}

.status-badge.pending {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

/* ========== الاستجابة للشاشات الصغيرة ========== */
@media (max-width: 1024px) {
  .admin-sidebar {
    transform: translateX(100%);
    width: 100%;
    max-width: 320px;
  }
  
  .admin-sidebar.show {
    transform: translateX(0);
  }
  
  .admin-content {
    margin-right: 0;
  }
  
  .admin-header {
    padding-right: 60px;
  }
  
  .sidebar-toggle {
    display: block;
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 1001;
    background-color: var(--primary-color);
    color: var(--text-white);
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    cursor: pointer;
    box-shadow: var(--shadow-lg);
  }
}

@media (min-width: 1025px) {
  .sidebar-toggle {
    display: none;
  }
}

@media (max-width: 768px) {
  .admin-main {
    padding: var(--spacing-lg);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .admin-table-container {
    overflow-x: auto;
  }
  
  .admin-table {
    min-width: 600px;
  }
  
  .admin-header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }
}

/* ========== بطاقة الترحيب المذهلة ========== */
.welcome-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(240, 249, 255, 0.9) 50%, rgba(224, 242, 254, 0.85) 100%);
  border: 1px solid rgba(14, 165, 233, 0.3);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow:
    0 20px 40px rgba(14, 165, 233, 0.15),
    0 8px 25px rgba(56, 189, 248, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(15px);
}

.welcome-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #0ea5e9, #38bdf8, #7dd3fc, #38bdf8, #0ea5e9);
  background-size: 300% 100%;
  animation: wave 4s ease-in-out infinite;
}

.welcome-card::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(14, 165, 233, 0.05) 0%, transparent 50%);
  animation: rotate 20s linear infinite;
}

@keyframes wave {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.welcome-card h1 {
  position: relative;
  z-index: 2;
  background: linear-gradient(135deg, #0ea5e9, #0284c7, #0369a1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(14, 165, 233, 0.2);
}

.welcome-card p {
  position: relative;
  z-index: 2;
}

/* ========== بطاقات الإجراءات السريعة ========== */
.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.action-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(240, 249, 255, 0.9) 100%);
  border: 1px solid rgba(14, 165, 233, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow:
    0 8px 25px rgba(14, 165, 233, 0.1),
    0 4px 15px rgba(56, 189, 248, 0.05);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-400), var(--primary-600), var(--primary-400));
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 15px 35px rgba(14, 165, 233, 0.2),
    0 8px 20px rgba(56, 189, 248, 0.1);
}

.action-card h5 {
  color: #0ea5e9;
  margin-bottom: 1rem;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.btn-custom {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  margin: 0.25rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(14, 165, 233, 0.3);
  position: relative;
  overflow: hidden;
}

.btn-custom::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s ease;
}

.btn-custom:hover {
  background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(14, 165, 233, 0.4);
  color: white;
}

.btn-custom:hover::before {
  left: 100%;
}

/* ========== تحسينات إضافية للتفاعل ========== */

/* تأثير الإضاءة للبطاقات */
.stat-card:hover {
  animation: pulseGlow 1s ease-in-out;
}

/* تأثير التموج للأزرار */
.btn-custom {
  position: relative;
  overflow: hidden;
}

.btn-custom:active {
  transform: scale(0.98);
}

/* تأثير الانزلاق للعناصر */
.admin-sidebar .nav-link {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.admin-sidebar .nav-link:hover {
  transform: translateX(-5px);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

/* تأثير التدرج المتحرك للخلفية */
.admin-content {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #bae6fd 50%, #f0f9ff 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

/* تحسين الشريط الجانبي */
.admin-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, rgba(14, 165, 233, 0.1) 0%, transparent 50%, rgba(14, 165, 233, 0.05) 100%);
  pointer-events: none;
}

/* تأثير الظل المتحرك للبطاقات */
.welcome-card,
.action-card {
  animation: shadowDance 6s ease-in-out infinite;
}


:root {
    --primary-color: #0ea5e9;           /* اللون السماوي الأساسي */
    --secondary-color: #0284c7;         /* سماوي أغمق قليلاً */
    --accent-color: #38bdf8;            /* سماوي فاتح للتأكيد */
    --success-color: #10b981;           /* نجاح */
    --warning-color: #f59e0b;           /* تحذير */
    --dark-color: #0c4a6e;              /* سماوي داكن */
    --light-color: #f0f9ff;             /* سماوي فاتح جداً */
    --white: #ffffff;
    --text-dark: #0c4a6e;
    --text-light: #0369a1;
    --bg-light: #f0f9ff;
    --shadow: 0 8px 25px rgba(14, 165, 233, 0.15);
    --shadow-lg: 0 20px 40px rgba(14, 165, 233, 0.25);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #bae6fd 50%, #7dd3fc 75%, #38bdf8 100%);
            background-attachment: fixed;
            color: var(--text-dark);
            line-height: 1.6;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(56, 189, 248, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(125, 211, 252, 0.05) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(10px) rotate(-1deg); }
        }

        .navbar-custom {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--dark-color) 100%);
            box-shadow:
                0 8px 32px rgba(14, 165, 233, 0.2),
                0 4px 16px rgba(56, 189, 248, 0.1);
            padding: 1rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .navbar-custom::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: slide 4s ease-in-out infinite;
        }

        @keyframes slide {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }

        .navbar-brand {
            color: var(--white) !important;
            font-weight: 700;
            font-size: 1.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .navbar-brand:hover {
            transform: scale(1.05);
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 0.75rem 1rem !important;
            border-radius: 10px;
            margin: 0 0.25rem;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .nav-link:hover {
            color: var(--white) !important;
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.25);
            color: var(--white) !important;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .container-main {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .welcome-card {
            background: var(--white);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            border-left: 5px solid var(--primary-color);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--white);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--white);
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-light);
            font-size: 1rem;
            font-weight: 500;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .action-card {
            background: var(--white);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
        }

        .action-card h5 {
            color: var(--text-dark);
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .btn-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: var(--white);
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 0.5rem;
            width: 100%;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
            color: var(--white);
        }

     

        .dropdown-item:hover {
            background: var(--bg-light);
        }

        /* Notification Styles */
      
      
       

        @media (max-width: 768px) {
            .container-main {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
    
        .notification-status-indicator {
            position: static;
            top: -2px;
            right: -2px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
            background-color: #dc3545; /* Red by default (disabled) */
            transition: background-color 0.3s ease;
        }

        .notification-status-indicator.enabled {
            background-color: #4bf773; /* Green when enabled */
        }

        .notification-status-indicator.pending {
            background-color: #ffc107; /* Yellow when pending */
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: bold;
            min-width: 18px;
            text-align: center;
            animation: bounce 0.5s ease-in-out;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-5px); }
            60% { transform: translateY(-3px); }
        }

        .alert-sm {
            padding: 0.5rem;
            font-size: 0.875rem;
        }
        .notification-status-dot {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #dc3545;
            transition: background-color 0.3s ease;
        }

        .notification-status-dot.enabled {
            background-color: #28a745;
        }

        .notification-status-dot.pending {
            background-color: #ffc107;
            animation: pulse 1.5s infinite;
        }
        
        /* زر الجرس مع العدد */
        #notificationDropdown {
            position: relative;
            font-size: 1.2rem;
        }
        #notificationDropdown .fa-bell {
            color: #fff;
            transition: color 0.3s ease;
        }
        #notificationDropdown:hover .fa-bell {
            color: #22c55e; /* لون أزرق Bootstrap */
        }

        /* النقطة الصغيرة للإشعارات الجديدة */
        #notificationStatusDot {
            position: absolute;
            top: 6px;
            right: 8px;
            width: 10px;
            height: 10px;
            background: #dc3545; /* أحمر */
            border-radius: 50%;
            border: 2px solid white;
            display: none; /* تظهر فقط عند وجود إشعارات جديدة */
            z-index: 10;
        }

        /* علامة عدد الإشعارات */
        #notificationBadge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #d10d17;
            color: white;
            font-size: 0.65rem;
            font-weight: 700;
            min-width: 18px;
            height: 18px;
            line-height: 18px;
            border-radius: 9px;
            text-align: center;
            padding: 0 5px;
            z-index: 10;
        }

            /* وضع القائمة بشكل مطلق فوق كل شيء */
        .dropdown-menu {
            width: 360px !important;
            position: absolute !important;
            top: 100% !important;  /* يضمن أنها تظهر تحت الزر */
            left: auto !important;  /* أو حسب المحاذاة */
            right: 0 !important;    /* لمحاذاة القائمة لليمين */
            max-height: 400px;      /* ارتفاع قابل للتمرير */
            overflow-y: auto;       /* تفعيل التمرير العمودي */
            z-index: 9999 !important; /* تأكد أنها فوق كل شيء */
            background: #f9f9f9;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            padding: 0;
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            text-align: right;
        }

        .dropdown-admin {
            width: 150px !important;
        }

        .dropdown-header {
            background: #22c55e;
            color: white;
            font-weight: 600;
            padding: 12px 20px;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
        }

        /* تقسيمات */
        .dropdown-divider {
            margin: 0;
            border-top: 1px solid #dee2e6;
        }

        /* تنسيق عنصر الإشعار */
        .notification-item {
            cursor: pointer;
            padding: 12px 20px;
            transition: background-color 0.3s ease;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }
        .notification-item:hover {
            background-color: #e9f0ff;
        }
        .notification-item.unread {
            background-color: #d0e2ff;
        }

        .notification-icon {
            font-size: 1.5rem;
            color: #22c55e;
            flex-shrink: 0;
        }

        .notification-title {
            font-weight: 700;
            font-size: 1rem;
            margin-bottom: 3px;
            color: #222;
        }

        .notification-message {
            font-size: 0.85rem;
            color: #555;
            white-space: pre-line;
        }

        .notification-time {
            font-size: 0.75rem;
            color: #888;
            margin-top: 6px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .notification-dot {
            width: 10px;
            height: 10px;
            background: #d10d17;
            border-radius: 50%;
            margin-left: 8px;
            flex-shrink: 0;
        }

        /* تنسيق تنبيه السماح بالإشعارات */
        #permissionAlert {
            font-size: 0.9rem;
            background: #fff3cd;
            border-color: #ffeeba;
            color: #856404;
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 16px;
            border-radius: 8px;
            margin: 12px 16px;
        }

        #permissionAlert i {
            font-size: 1.3rem;
        }

        #enableNotificationsBtn {
            margin-left: auto;
        }

        /* زر عرض جميع الإشعارات */
        .dropdown-item-text .btn {
            width: 100%;
            font-weight: 600;
            border-radius: 8px;
        }

        /* Spinner صغير */
        #notificationsList .spinner-border {
            color: #22c55e;
        }

        /* السماح للـ navbar بأن تظهر القوائم خارجه */
        .navbar, .navbar-nav {
        overflow: visible !important;
        position: relative !important; /* لو عندك position ثابت أو absolute جرب تغيره */
        }


