'use strict';
const { Model, Op } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Notification extends Model {
    static associate(models) {
      Notification.belongsTo(models.Customer, {
        foreignKey: 'customerId',
        as: 'customer',
        allowNull: true
      });
      Notification.belongsTo(models.DeliveryPerson, { // <-- لاحظ الحرف الكبير
        foreignKey: 'deliveryPersonId',
        as: 'deliveryPerson',
        allowNull: true
      });
      // Admin model not available - commented out
       Notification.belongsTo(models.Admin, {
         foreignKey: 'adminId',
         as: 'admin',
         allowNull: true
       });
    }

    // Instance methods
    async markAsRead() {
      return await this.update({ readAt: new Date() });
    }

    async markAsUnread() {
      return await this.update({ readAt: null });
    }

    isRead() {
      return this.readAt !== null;
    }

    // Static methods
    static async createNotification(data) {
      const notification = await this.create({
        title: data.title,
        message: data.message,
        type: data.type || 'info',
        priority: data.priority || 'normal',
        customerId: data.customerId || null,
        adminId: data.adminId || null,
        data: data.data ? JSON.stringify(data.data) : null,
        expiresAt: data.expiresAt || null
      });
      return notification;
    }

    static async getUnreadCount(userId, userType = 'customer') {
      const whereClause = {};
      if (userType === 'customer') {
        whereClause.customerId = userId;
      } else if (userType === 'admin') {
        whereClause.adminId = userId;
      }

      return await this.count({
        where: {
          ...whereClause,
          readAt: null,
          expiresAt: {
            [Op.or]: [
              null,
              { [Op.gt]: new Date() }
            ]
          }
        }
      });
    }

    static async markAllAsRead(userId, userType = 'customer') {
      const whereClause = {};
      if (userType === 'customer') {
        whereClause.customerId = userId;
      } else if (userType === 'admin') {
        whereClause.adminId = userId;
      }

      return await this.update(
        { readAt: new Date() },
        {
          where: {
            ...whereClause,
            readAt: null
          }
        }
      );
    }
  }

  Notification.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 255]
      }
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'info',
      validate: {
        isIn: [['info', 'success', 'warning', 'error', 'order', 'promotion', 'system']]
      }
    },
    priority: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'normal',
      validate: {
        isIn: [['low', 'normal', 'high', 'urgent']]
      }
    },
    customerId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Customers',
        key: 'id'
      }
    },
    adminId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Admins',
        key: 'id'
      }
    },
    deliveryPersonId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'deliverypeople',
        key: 'id'
      }
    },
    data: {
      type: DataTypes.TEXT,
      allowNull: true,
      get() {
        const rawValue = this.getDataValue('data');
        return rawValue ? JSON.parse(rawValue) : null;
      },
      set(value) {
        this.setDataValue('data', value ? JSON.stringify(value) : null);
      }
    },
    readAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    actionUrl: {
      type: DataTypes.STRING,
      allowNull: true
    },
    actionText: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'Notification',
    tableName: 'Notifications',
    timestamps: true,
    indexes: [
      {
        fields: ['customerId', 'readAt']
      },
      {
        fields: ['adminId', 'readAt']
      },
      {
        fields: ['type']
      },
      {
        fields: ['deliveryPersonId', 'readAt']
      },
      {
        fields: ['priority']
      },
      {
        fields: ['createdAt']
      }
    ],
    scopes: {
      unread: {
        where: {
          readAt: null
        }
      },
      read: {
        where: {
          readAt: {
            [Op.not]: null
          }
        }
      },
      active: {
        where: {
          expiresAt: {
            [Op.or]: [
              null,
              { [Op.gt]: new Date() }
            ]
          }
        }
      }
    }
  });

  return Notification;
};