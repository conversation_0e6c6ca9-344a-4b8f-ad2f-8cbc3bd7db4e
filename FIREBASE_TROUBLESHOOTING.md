# دليل حل مشاكل إشعارات Firebase

## المشاكل الشائعة وحلولها

### 1. خطأ المصادقة (Auth Error from APNS or Web Push Service)

#### الأسباب المحتملة:
- **مفاتيح VAPID غير صحيحة أو منتهية الصلاحية**
- **إعدادات Firebase غير متطابقة**
- **شهادات APNS غير صالحة**
- **توكنات FCM منتهية الصلاحية**

#### الحلول:

##### أ) فحص إعدادات Firebase
```bash
# تشغيل فحص شامل
node scripts/firebase-diagnostics.js full

# فحص التوكنات فقط
node scripts/firebase-diagnostics.js tokens

# اختبار الإرسال
node scripts/firebase-diagnostics.js test
```

##### ب) تحديث مفاتيح VAPID
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروعك
3. اذهب إلى Project Settings > Cloud Messaging
4. في قسم Web configuration، انسخ مفتاح VAPID الجديد
5. حدث المفتاح في `config/firebase.js`

##### ج) فحص شهادات APNS (للتطبيقات المحمولة)
1. تأكد من رفع شهادة APNS الصحيحة في Firebase Console
2. تحقق من انتهاء صلاحية الشهادة
3. تأكد من استخدام شهادة Production للبيئة الإنتاجية

### 2. مشاكل Service Worker

#### الأعراض:
- عدم تلقي الإشعارات في الخلفية
- أخطاء في تسجيل Service Worker

#### الحلول:
```javascript
// فحص تسجيل Service Worker
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.getRegistrations().then(registrations => {
    console.log('Service Workers:', registrations);
  });
}

// إعادة تسجيل Service Worker
navigator.serviceWorker.register('/firebase-messaging-sw.js', {
  scope: '/'
}).then(registration => {
  console.log('Service Worker registered:', registration);
}).catch(error => {
  console.error('Service Worker registration failed:', error);
});
```

### 3. مشاكل التوكنات

#### تنظيف التوكنات غير الصالحة:
```bash
node scripts/firebase-diagnostics.js cleanup
```

#### فحص صحة التوكنات يدوياً:
```javascript
const FirebaseMessagingService = require('./services/FirebaseMessagingService');

// فحص توكن محدد
const isValid = await FirebaseMessagingService.validateToken('YOUR_TOKEN_HERE');
console.log('Token valid:', isValid);
```

### 4. مشاكل الشبكة والاتصال

#### فحص الاتصال بخوادم Firebase:
```bash
# فحص الاتصال
curl -I https://fcm.googleapis.com/fcm/send

# فحص DNS
nslookup fcm.googleapis.com
```

#### إعدادات Firewall:
تأكد من السماح للمنافذ التالية:
- 443 (HTTPS)
- 5228-5230 (FCM)

### 5. مشاكل المتصفح

#### المتصفحات المدعومة:
- Chrome 50+
- Firefox 44+
- Safari 16+ (macOS 13+)
- Edge 17+

#### إعدادات المتصفح:
1. تأكد من تفعيل الإشعارات في إعدادات المتصفح
2. تحقق من عدم حظر الموقع
3. امسح cache و cookies

### 6. أخطاء شائعة ورسائلها

#### `messaging/invalid-argument`
- **السبب**: توكن FCM غير صالح
- **الحل**: حذف التوكن وإعادة التسجيل

#### `messaging/registration-token-not-registered`
- **السبب**: التوكن لم يعد مسجلاً
- **الحل**: حذف التوكن من قاعدة البيانات

#### `messaging/mismatched-credential`
- **السبب**: عدم تطابق بيانات المصادقة
- **الحل**: فحص Service Account وإعدادات المشروع

#### `messaging/third-party-auth-error`
- **السبب**: مشكلة في مصادقة APNS
- **الحل**: فحص شهادات APNS

### 7. أدوات التشخيص

#### استخدام أدوات المطور:
```javascript
// في Console المتصفح
// فحص Service Worker
navigator.serviceWorker.getRegistrations().then(console.log);

// فحص إذن الإشعارات
console.log('Notification permission:', Notification.permission);

// اختبار إشعار محلي
new Notification('Test', { body: 'Test notification' });
```

#### فحص Network في أدوات المطور:
1. افتح أدوات المطور (F12)
2. اذهب إلى تبويب Network
3. ابحث عن طلبات FCM
4. فحص رموز الاستجابة والأخطاء

### 8. البيئة الإنتاجية

#### متطلبات HTTPS:
- يجب استخدام HTTPS في البيئة الإنتاجية
- شهادة SSL صالحة مطلوبة
- Service Worker يعمل فقط مع HTTPS

#### إعدادات الخادم:
```nginx
# إعدادات Nginx للـ Service Worker
location /firebase-messaging-sw.js {
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
}
```

### 9. المراقبة والسجلات

#### تفعيل السجلات المفصلة:
```javascript
// في firebase-messaging-sw.js
console.log('Service Worker loaded');

messaging.onBackgroundMessage((payload) => {
  console.log('Background message received:', payload);
});
```

#### مراقبة الأداء:
```javascript
// قياس وقت الاستجابة
const start = Date.now();
await FirebaseMessagingService.sendToAllAdmins(notification, data);
console.log(`Notification sent in ${Date.now() - start}ms`);
```

### 10. الاختبار

#### اختبار محلي:
```bash
# تشغيل الخادم محلياً مع HTTPS
npm install -g local-ssl-proxy
local-ssl-proxy --source 3001 --target 3000
```

#### اختبار الإنتاج:
```bash
# اختبار من سطر الأوامر
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "YOUR_TOKEN",
    "notification": {
      "title": "Test",
      "body": "Test message"
    }
  }'
```

## جهات الاتصال للدعم

- [Firebase Support](https://firebase.google.com/support)
- [Firebase Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Stack Overflow - Firebase](https://stackoverflow.com/questions/tagged/firebase)

## تحديثات مهمة

تأكد من مراجعة هذا الدليل بانتظام حيث قد تتغير إعدادات Firebase مع التحديثات.
