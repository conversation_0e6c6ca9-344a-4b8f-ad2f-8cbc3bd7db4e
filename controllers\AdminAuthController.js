const { Admin, AdminToken } = require('../models');
const { generateAuthTokens } = require('../utils/jwt');

class AdminAuthController {
    // Show login form
    async showLogin(req, res) {
        res.render('admin/auth/login', { layout: 'layouts/auth' });
    }

    // Handle login
    async login(req, res) {
        try {
            const { username, password } = req.body;
            const admin = await Admin.findOne({ where: { username } });
            if (!admin) {
                return res.render('admin/auth/login', {
                    layout: 'layouts/auth',
                    error: 'اسم المستخدم أو كلمة المرور غير صحيحة'
                });
            }

           const isValidPassword = await admin.validatePassword(password);
            if (!isValidPassword) {
                return res.render('admin/auth/login', {
                    layout: 'layouts/auth',
                    error: 'اسم المستخدم أو كلمة المرور غير صحيحة'
                });
            }

            if (admin.status !== 'active') {
                return res.render('admin/auth/login', {
                    layout: 'layouts/auth',
                    error: 'تم إلغاء تفعيل حسابك'
                });
            }

            // Update last login
            admin.lastLogin = new Date();
            await admin.save();

            // إنشاء JWT tokens
            const tokens = generateAuthTokens(admin, 'admin');

            // تعيين الـ token في الـ cookies
            res.cookie('token', tokens.accessToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                maxAge: 24 * 60 * 60 * 1000, // 24 hours
                sameSite: 'strict'
            });

            res.cookie('refreshToken', tokens.refreshToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
                sameSite: 'strict'
            });

            res.redirect('/admin/dashboard');
        } catch (error) {
            res.render('admin/auth/login', {
                error: 'An error occurred during login'
            });
        }
    }

    // Handle logout
    async logout(req, res) {
        // مسح الـ cookies
        res.clearCookie('token');
        res.clearCookie('refreshToken');

        res.redirect('/admin/auth/login?success=' + encodeURIComponent('Logged out successfully'));
    }

    // Change Password
    async showChangePassword(req, res) {
        res.render('admin/auth/change-password');
    }

    async changePassword(req, res) {
        try {
            const { currentPassword, newPassword, confirmPassword } = req.body;
            const admin = await Admin.findByPk(req.user.id);

            if (!admin) {
                return res.render('admin/auth/change-password', {
                    error: 'Admin not found'
                });
            }

            const isValidPassword = await admin.validatePassword(currentPassword);
            if (!isValidPassword) {
                return res.render('admin/auth/change-password', {
                    error: 'Current password is incorrect'
                });
            }

            if (newPassword !== confirmPassword) {
                return res.render('admin/auth/change-password', {
                    error: 'New passwords do not match'
                });
            }

            admin.password = newPassword;
            await admin.save();

            res.render('admin/auth/change-password', {
                success: 'Password changed successfully'
            });
        } catch (error) {
            res.render('admin/auth/change-password', {
                error: 'An error occurred while changing password'
            });
        }
    }

    async AdminToken(req, res) {
        const { token } = req.body;
        const adminId = req.session?.adminId || req.admin?.id;

        if (!token || !adminId) {
            return res.status(400).json({ success: false, message: 'توكن أو معرف الأدمن مفقود' });
        }

        try {
            const existingToken = await AdminToken.findOne({ where: { token } });

            if (existingToken) {
            // حدث التوكن الحالي
            await existingToken.update({
                adminId,
                updatedAt: new Date()
            });
            } else {
            // أنشئ توكن جديد
            await AdminToken.create({
                token,
                adminId,
                createdAt: new Date(),
                updatedAt: new Date()
            });
            }

            res.json({ success: true, message: 'تم حفظ توكن الإشعارات بنجاح' });

        } catch (err) {
            console.error('❌ خطأ في حفظ التوكن:', err);
            res.status(500).json({ success: false, message: 'خطأ في حفظ التوكن' });
        }
    }
}

module.exports = new AdminAuthController();