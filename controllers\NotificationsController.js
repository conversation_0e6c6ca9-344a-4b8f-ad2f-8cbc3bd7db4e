const BaseController = require('./BaseController');
const { Notification, Customer, Admin } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');
const NotificationsFunction = require('../controllers/NotificationsFunction');


class NotificationsController extends BaseController {
    constructor() {
        super(Notification, 'notifications');
    }

    // عرض إشعارات المدير الحالي
    async adminIndex(req, res) {
        try {
            const adminId = req.user.id;
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const offset = (page - 1) * limit;

            const { count, rows: notifications } = await Notification.findAndCountAll({
                where: {
                    adminId: adminId,
                },
                order: [['createdAt', 'DESC']],
                limit,
                offset
            });

            const totalPages = Math.ceil(count / limit);

            res.render('admin/notifications/index', {
                notifications,
                count,
                totalPages,
                currentPage: page
            });
        } catch (error) {
            logger.error('Error loading admin notifications:', error);
            res.status(500).render('error', { error });
        }
    }

    async getAdminNotifications(req, res){
       try {
        const adminId = req.user.id;    
            const notifications = await Notification.findAll({ 
                where: {adminId: adminId, readAt: null },
                order: [['createdAt', 'DESC']]});
            res.json({ success: true, notifications });
        } catch (error) {
            res.status(500).json({ success: false, message: 'خطأ في تحميل الإشعارات' });
        }
    }
    
    // إرسال إشعار مخصص
    async sendCustomNotification(req, res) {
        try {
        const { title, message, target, priority } = req.body;

        if (!title || !message) {
            return res.status(400).json({
            success: false,
            message: 'العنوان والرسالة مطلوبان'
            });
        }

        const FirebaseMessagingService = require('../services/FirebaseMessagingService');
        const NotificationService = require('../services/NotificationService');

        const notification = {
            title: title,
            body: message
        };

        const data = {
            type: 'custom',
            priority: priority || 'normal',
            timestamp: Date.now().toString(),
            clickAction: '/admin/dashboard'
        };

        let result = { successCount: 0, failureCount: 0 };

        // إرسال الإشعار حسب المستهدفين
        switch (target) {
            case 'all_admins':
            result = await FirebaseMessagingService.sendToAllAdmins(notification, data);
            break;
            case 'all_customers':
            await NotificationService.notifyAllCustomers({
                title: title,
                message: message,
                type: 'info',
                priority: priority || 'normal'
            });
            result.successCount = 1; // تقدير
            break;
            case 'all':
            const [adminResult] = await Promise.all([
                FirebaseMessagingService.sendToAllAdmins(notification, data),
                NotificationService.notifyAll({
                title: title,
                message: message,
                type: 'info',
                priority: priority || 'normal'
                })
            ]);
            result = adminResult;
            result.successCount += 1; // إضافة العملاء
            break;
            default:
            return res.status(400).json({
                success: false,
                message: 'مستهدف غير صحيح'
            });
        }


        res.json({
            success: true,
            message: 'تم إرسال الإشعار بنجاح',
            sentCount: result.successCount
        });

        } catch (error) {
        console.error('❌ Error sending custom notification:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إرسال الإشعار: ' + error.message
        });
        }
    }

    // عرض تفاصيل الإشعار
    async showNotification(req, res) {
        try {
            const notification = await Notification.findByPk(req.params.id, {
                include: [
                    { model: Customer, as: 'customer', required: false },
                    { model: Admin, as: 'admin', required: false }
                ]
            });

            if (!notification) {
                return res.status(404).render('error', {
                    error: { status: 404, message: 'الإشعار غير موجود' }
                });
            }

            res.render('notifications/show', { notification });
        } catch (error) {
            logger.error('Error showing notification:', error);
            res.status(500).render('error', { error });
        }
    }

    // تحديد إشعار كمقروء
    async markNotificationAsRead(req, res) {
        try {
        const { id } = req.params;
        const Notification = require('../models').Notification;

        await Notification.update(
            { readAt: new Date() },
            { where: { id: id } }
        );

        res.json({ success: true });
        } catch (error) {
        console.error('❌ Error marking notification as read:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تحديث الإشعار'
        });
        }
    }

    // حذف إشعار
    async deleteNotification(req, res) {
        try {
        const { id } = req.params;
        const Notification = require('../models').Notification;

        await Notification.destroy({ where: { id: id } });

        res.json({ success: true });
        } catch (error) {
        console.error('❌ Error deleting notification:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حذف الإشعار'
        });
        }
    }

    // تحديد جميع الإشعارات كمقروءة
    async markAllNotificationsAsRead(req, res) {
        try {
        const Notification = require('../models').Notification;

        await Notification.update(
            { readAt: new Date() },
            { where: { readAt: null } }
        );

        res.json({ success: true });
        } catch (error) {
        console.error('❌ Error marking all notifications as read:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تحديث الإشعارات'
        });
        }
    }

    // مسح جميع الإشعارات
    async clearAllNotifications(req, res) {
        try {
        const Notification = require('../models').Notification;

        await Notification.destroy({ where: {} });

        res.json({ success: true });
        } catch (error) {
        console.error('❌ Error clearing all notifications:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حذف الإشعارات'
        });
        }
    }
}

module.exports = new NotificationsController();
