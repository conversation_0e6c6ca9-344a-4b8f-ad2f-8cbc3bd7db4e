#!/usr/bin/env node

/**
 * حذف جميع التوكنات القديمة
 */

require('dotenv').config();
const { AdminToken } = require('../models');

async function clearTokens() {
  try {
    console.log('🗑️ حذف جميع التوكنات القديمة...');
    
    const deletedCount = await AdminToken.destroy({ 
      where: {},
      truncate: true 
    });
    
    console.log(`✅ تم حذف ${deletedCount} توكن`);
    console.log('💡 يمكنك الآن تسجيل توكن جديد من لوحة التحكم');
    
  } catch (error) {
    console.error('❌ خطأ في حذف التوكنات:', error.message);
  } finally {
    process.exit(0);
  }
}

clearTokens();
