<div class="container mt-4">

  <h3 class="mb-4">التوصيلات الخاصة بـ <%= deliveryPersonName %></h3>

  <!-- البحث والفلترة المباشرة -->
  <div class="row mb-4">
    <div class="col-md-4">
      <input type="text" class="form-control" placeholder="البحث في التوصيلات..." id="searchInput">
    </div>
    <div class="col-md-3">
      <select class="form-select" id="statusFilter">
        <option value="">جميع الحالات</option>
        <option value="in_transit">قيد التوصيل</option>
        <option value="delivered">تم التوصيل</option>
        <option value="cancelled">ملغي</option>
      </select>
    </div>
    <div class="col-md-3">
      <input type="date" class="form-control" id="dateFilter" title="تصفية حسب التاريخ">
    </div>
    <div class="col-md-2">
      <button class="btn btn-secondary w-100" onclick="clearFilters()">
        <i class="fas fa-times"></i> مسح
      </button>
    </div>
  </div>

  <!-- ✅ جدول التوصيلات الجارية -->
  <div class="mb-5">
    <h4 class="mb-3">التوصيلات الجارية</h4>
    <table class="table table-bordered table-striped">
      <thead class="table-light">
        <tr>
          <th>الطلب</th>
          <th>السائق</th>
          <th>العميل</th>
          <th>الحالة</th>
          <th>وقت الاستلام</th>
          <th>إجراءات</th>
        </tr>
      </thead>
      <tbody>
        <% inProgressDeliveries.forEach(delivery => { %>
          <tr class="delivery-item in-progress"
              data-order-id="<%= delivery.order.id %>"
              data-customer="<%= delivery.order.customer.name %>"
              data-courier="<%= delivery.courier.name %>"
              data-status="<%= delivery.status %>"
              data-date="<%= new Date(delivery.createdAt).toISOString().split('T')[0] %>">
            <td>
              <a href="/orders/<%= delivery.order.id %>">
                #<%= delivery.order.id %>
              </a>
            </td>
            <td>
              <a href="drivers/<%= delivery.courier.id %>/deliveries">
                <%= delivery.courier.name %>
              </a>
            </td>
            <td>
              <a href="/customers/<%= delivery.order.customer.id %>">
                <%= delivery.order.customer.name %>
              </a>
            </td>
            <%
              let badgeClass = 'secondary';
              let statusText = 'غير معروف';
              switch (delivery.status) {
                case 'in_transit':
                  badgeClass = 'info';
                  statusText = 'قيد التوصيل';
                  break;
                case 'delivered':
                  badgeClass = 'success';
                  statusText = 'تم التوصيل';
                  break;
                case 'cancelled':
                  badgeClass = 'danger';
                  statusText = 'ملغي';
                  break;
              }
            %>

            <td>
              <span class="badge bg-<%= badgeClass %>">
                <%= statusText %>
              </span>
            </td>
            <td><%= delivery.pickupTime ? delivery.pickupTime.toLocaleString() : '-' %></td>
            <td>
              <form action="/admin/deliveries/<%= delivery.id %>/complete" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من تأكيد التوصيل؟');">
                <button type="submit" class="btn btn-sm btn-success">
                  تم التوصيل
                </button>
              </form>
              <a href="/admin/deliveries/<%= delivery.id %>/edit" class="btn btn-sm btn-secondary ms-1">
                تعديل
              </a>
              <form action="/admin/deliveries/<%= delivery.id %>/delete" method="POST" class="d-inline">
                <input type="hidden" name="_method" value="DELETE">
                <button type="submit" class="btn btn-sm btn-danger ms-1" onclick="return confirm('هل أنت متأكد من حذف التوصيل؟')">
                  حذف
                </button>
              </form>
            </td>
          </tr>
        <% }) %>
      </tbody>
    </table>
  </div>

  <!-- ✅ جدول التوصيلات المكتملة أو الملغاة -->
  <div>
    <h4 class="mb-3">التوصيلات المكتملة / الملغاة</h4>
    <table class="table table-bordered table-striped">
      <thead class="table-light">
        <tr>
          <th>الطلب</th>
          <th>العميل</th>
          <th>الحالة</th>
          <th>وقت التوصيل</th>
        </tr>
      </thead>
      <tbody>
        <% completedDeliveries.forEach(delivery => { %>
          <tr class="delivery-item completed"
              data-order-id="<%= delivery.order.id %>"
              data-customer="<%= delivery.order.customer.name %>"
              data-status="<%= delivery.status %>"
              data-date="<%= new Date(delivery.createdAt).toISOString().split('T')[0] %>">
            <td>
              <a href="/admin/orders/<%= delivery.order.id %>">
                #<%= delivery.order.id %>
              </a>
            </td>
            <td>
              <a href="/admin/customers/<%= delivery.order.customer.id %>">
                <%= delivery.order.customer.name %>
              </a>
            </td>
                 <%
              let badgeClass = 'secondary';
              let statusText = 'غير معروف';
              switch (delivery.status) {
                case 'in_transit':
                  badgeClass = 'info';
                  statusText = 'قيد التوصيل';
                  break;
                case 'delivered':
                  badgeClass = 'success';
                  statusText = 'تم التوصيل';
                  break;
                case 'cancelled':
                  badgeClass = 'danger';
                  statusText = 'ملغي';
                  break;
              }
            %>
            <td>
              <span class="badge bg-<%= badgeClass %>">
                <%= statusText %>
              </span>
            </td>
            <td><%= delivery.deliveryTime ? delivery.deliveryTime.toLocaleString() : '-' %></td>
          </tr>
        <% }) %>
      </tbody>
    </table>
  </div>

</div>

<script>
  // البحث والفلترة المباشرة للتوصيلات
  document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('searchInput').addEventListener('input', filterDeliveries);
    document.getElementById('statusFilter').addEventListener('change', filterDeliveries);
    document.getElementById('dateFilter').addEventListener('change', filterDeliveries);
  });

  function filterDeliveries() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const dateFilter = document.getElementById('dateFilter').value;

    const deliveries = document.querySelectorAll('.delivery-item');
    let visibleCount = 0;

    deliveries.forEach(delivery => {
      const orderId = delivery.dataset.orderId.toLowerCase();
      const customer = delivery.dataset.customer.toLowerCase();
      const courier = delivery.dataset.courier ? delivery.dataset.courier.toLowerCase() : '';
      const status = delivery.dataset.status;
      const date = delivery.dataset.date;

      const matchesSearch = searchTerm === '' ||
                          orderId.includes(searchTerm) ||
                          customer.includes(searchTerm) ||
                          courier.includes(searchTerm);
      const matchesStatus = !statusFilter || status === statusFilter;
      const matchesDate = !dateFilter || date === dateFilter;

      if (matchesSearch && matchesStatus && matchesDate) {
        delivery.style.display = '';
        visibleCount++;
      } else {
        delivery.style.display = 'none';
      }
    });

    // إظهار رسالة عدم وجود نتائج
    updateNoResultsMessage(visibleCount);
  }

  function updateNoResultsMessage(visibleCount) {
    let noResultsRow = document.getElementById('noResultsRow');

    if (visibleCount === 0) {
      if (!noResultsRow) {
        // إضافة رسالة للجدول الأول (التوصيلات الجارية)
        const inProgressTable = document.querySelector('.mb-5 tbody');
        if (inProgressTable) {
          const noResultsRowInProgress = document.createElement('tr');
          noResultsRowInProgress.id = 'noResultsRowInProgress';
          noResultsRowInProgress.innerHTML = `
            <td colspan="6" class="text-center py-4">
              <div class="text-muted">
                <i class="fas fa-search fa-3x mb-3"></i>
                <p class="mb-0">لا توجد توصيلات جارية مطابقة لمعايير البحث</p>
              </div>
            </td>
          `;
          inProgressTable.appendChild(noResultsRowInProgress);
        }

        // إضافة رسالة للجدول الثاني (التوصيلات المكتملة)
        const completedTable = document.querySelector('div:last-of-type tbody');
        if (completedTable) {
          const noResultsRowCompleted = document.createElement('tr');
          noResultsRowCompleted.id = 'noResultsRowCompleted';
          noResultsRowCompleted.innerHTML = `
            <td colspan="4" class="text-center py-4">
              <div class="text-muted">
                <i class="fas fa-search fa-3x mb-3"></i>
                <p class="mb-0">لا توجد توصيلات مكتملة مطابقة لمعايير البحث</p>
              </div>
            </td>
          `;
          completedTable.appendChild(noResultsRowCompleted);
        }
      } else {
        document.getElementById('noResultsRowInProgress')?.style.setProperty('display', '');
        document.getElementById('noResultsRowCompleted')?.style.setProperty('display', '');
      }
    } else {
      document.getElementById('noResultsRowInProgress')?.style.setProperty('display', 'none');
      document.getElementById('noResultsRowCompleted')?.style.setProperty('display', 'none');
    }
  }

  function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('dateFilter').value = '';
    filterDeliveries();
  }
</script>
