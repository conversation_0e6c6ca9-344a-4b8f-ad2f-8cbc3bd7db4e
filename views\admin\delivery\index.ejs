<div class="container mt-4">

  <h3 class="mb-4">التوصيلات الخاصة بـ <%= deliveryPersonName %></h3>

  <!-- ✅ جدول التوصيلات الجارية -->
  <div class="mb-5">
    <h4 class="mb-3">التوصيلات الجارية</h4>
    <table class="table table-bordered table-striped">
      <thead class="table-light">
        <tr>
          <th>الطلب</th>
          <th>السائق</th>
          <th>العميل</th>
          <th>الحالة</th>
          <th>وقت الاستلام</th>
          <th>إجراءات</th>
        </tr>
      </thead>
      <tbody>
        <% inProgressDeliveries.forEach(delivery => { %>
          <tr>
            <td>
              <a href="/orders/<%= delivery.order.id %>">
                #<%= delivery.order.id %>
              </a>
            </td>
            <td>
              <a href="drivers/<%= delivery.courier.id %>/deliveries">
                <%= delivery.courier.name %>
              </a>
            </td>
            <td>
              <a href="/customers/<%= delivery.order.customer.id %>">
                <%= delivery.order.customer.name %>
              </a>
            </td>
            <%
              let badgeClass = 'secondary';
              let statusText = 'غير معروف';
              switch (delivery.status) {
                case 'in_transit':
                  badgeClass = 'info';
                  statusText = 'قيد التوصيل';
                  break;
                case 'delivered':
                  badgeClass = 'success';
                  statusText = 'تم التوصيل';
                  break;
                case 'cancelled':
                  badgeClass = 'danger';
                  statusText = 'ملغي';
                  break;
              }
            %>

            <td>
              <span class="badge bg-<%= badgeClass %>">
                <%= statusText %>
              </span>
            </td>
            <td><%= delivery.pickupTime ? delivery.pickupTime.toLocaleString() : '-' %></td>
            <td>
              <form action="/admin/deliveries/<%= delivery.id %>/complete" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من تأكيد التوصيل؟');">
                <button type="submit" class="btn btn-sm btn-success">
                  تم التوصيل
                </button>
              </form>
              <a href="/admin/deliveries/<%= delivery.id %>/edit" class="btn btn-sm btn-secondary ms-1">
                تعديل
              </a>
              <form action="/admin/deliveries/<%= delivery.id %>/delete" method="POST" class="d-inline">
                <input type="hidden" name="_method" value="DELETE">
                <button type="submit" class="btn btn-sm btn-danger ms-1" onclick="return confirm('هل أنت متأكد من حذف التوصيل؟')">
                  حذف
                </button>
              </form>
            </td>
          </tr>
        <% }) %>
      </tbody>
    </table>
  </div>

  <!-- ✅ جدول التوصيلات المكتملة أو الملغاة -->
  <div>
    <h4 class="mb-3">التوصيلات المكتملة / الملغاة</h4>
    <table class="table table-bordered table-striped">
      <thead class="table-light">
        <tr>
          <th>الطلب</th>
          <th>العميل</th>
          <th>الحالة</th>
          <th>وقت التوصيل</th>
        </tr>
      </thead>
      <tbody>
        <% completedDeliveries.forEach(delivery => { %>
          <tr>
            <td>
              <a href="/admin/orders/<%= delivery.order.id %>">
                #<%= delivery.order.id %>
              </a>
            </td>
            <td>
              <a href="/admin/customers/<%= delivery.order.customer.id %>">
                <%= delivery.order.customer.name %>
              </a>
            </td>
                 <%
              let badgeClass = 'secondary';
              let statusText = 'غير معروف';
              switch (delivery.status) {
                case 'in_transit':
                  badgeClass = 'info';
                  statusText = 'قيد التوصيل';
                  break;
                case 'delivered':
                  badgeClass = 'success';
                  statusText = 'تم التوصيل';
                  break;
                case 'cancelled':
                  badgeClass = 'danger';
                  statusText = 'ملغي';
                  break;
              }
            %>
            <td>
              <span class="badge bg-<%= badgeClass %>">
                <%= statusText %>
              </span>
            </td>
            <td><%= delivery.deliveryTime ? delivery.deliveryTime.toLocaleString() : '-' %></td>
          </tr>
        <% }) %>
      </tbody>
    </table>
  </div>

</div>
