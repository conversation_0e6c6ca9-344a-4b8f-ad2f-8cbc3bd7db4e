<div class="container mt-4">
    <h2 class="mb-4">لوحة إدارة التوصيلات</h2>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped text-center">
                    <thead class="table-light">
                        <tr>
                            <th>رقم التوصيل</th>
                            <th>رقم الطلب</th>
                            <th>العميل</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% if (deliveries.length > 0) { %>
                            <% deliveries.forEach(delivery => { %>
                                <tr>
                                    <td><%= delivery.id %></td>
                                    <td>
                                        <a href="/admin/orders/<%= delivery.order.id %>">
                                            #<%= delivery.order.id %>
                                        </a>
                                    </td>
                                    <td>
                                        <a href="/admin/customers/<%= delivery.order.customer.id %>">
                                            <%= delivery.order.customer.name || 'عميل غير معروف' %>
                                        </a>
                                    </td>
                                    <%
                                    let badgeClass = 'secondary';
                                    let statusText = 'غير معروف';

                                    switch (delivery.status) {
                                        case 'in_transit':
                                        badgeClass = 'info';
                                        statusText = 'قيد التوصيل';
                                        break;
                                        case 'delivered':
                                        badgeClass = 'success';
                                        statusText = 'تم التوصيل';
                                        break;
                                        case 'cancelled':
                                        badgeClass = 'danger';
                                        statusText = 'ملغي';
                                        break;
                                    }
                                    %>

                                    <td>
                                    <span class="badge bg-<%= badgeClass %>">
                                        <%= statusText %>
                                    </span>
                                    </td>
                                    <td><%= new Date(delivery.createdAt).toLocaleString('ar-EG') %></td>
                                    <td>
                                        <a href="/admin/deliveries/<%= delivery.order.id %>" class="btn btn-sm btn-info">
                                            تفاصيل
                                        </a>
                                        <a href="/admin/deliveries/<%= delivery.id %>/edit" class="btn btn-sm btn-primary">
                                            تعديل
                                        </a>
                                        <a href="/admin/deliveries/<%= delivery.id %>/delete" class="btn btn-sm btn-danger">
                                            حذف
                                        </a>
                                    </td>
                                </tr>
                            <% }) %>
                        <% } else { %>
                            <tr>
                                <td colspan="6" class="text-muted text-center">لا توجد توصيلات حالياً</td>
                            </tr>
                        <% } %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <% if (totalPages > 1) { %>
        <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
            <ul class="pagination">
                <% if (currentPage > 1) { %>
                    <li class="page-item">
                        <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
                    </li>
                <% } else { %>
                    <li class="page-item disabled">
                        <span class="page-link">السابق</span>
                    </li>
                <% } %>

                <% for(let i = 1; i <= totalPages; i++) { %>
                    <li class="page-item <%= currentPage === i ? 'active' : '' %>">
                        <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                    </li>
                <% } %>

                <% if (currentPage < totalPages) { %>
                    <li class="page-item">
                        <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
                    </li>
                <% } else { %>
                    <li class="page-item disabled">
                        <span class="page-link">التالي</span>
                    </li>
                <% } %>
            </ul>
        </nav>
    <% } %>
</div>
