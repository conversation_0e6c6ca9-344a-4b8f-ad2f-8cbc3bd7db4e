<div class="container mt-4">
    <h2 class="mb-4">لوحة إدارة التوصيلات</h2>


<!-- البحث والفلترة المباشرة -->
    <div class="row mb-4">
        <div class="col-md-4">
            <input type="text" class="form-control" placeholder="البحث في التوصيلات..." id="searchInput">
        </div>
        <div class="col-md-3">
            <select class="form-select" id="statusFilter">
                <option value="">جميع الحالات</option>
                <option value="in_transit">قيد التوصيل</option>
                <option value="delivered">تم التوصيل</option>
                <option value="cancelled">ملغي</option>
            </select>
        </div>
        <div class="col-md-3">
            <input type="date" class="form-control" id="dateFilter" title="تصفية حسب التاريخ">
        </div>
        <div class="col-md-2">
            <button class="btn btn-secondary w-100" onclick="clearFilters()">
                <i class="fas fa-times"></i> مسح
            </button>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped text-center">
                    <thead class="table-light">
                        <tr>
                            <th>رقم التوصيل</th>
                            <th>رقم الطلب</th>
                            <th>السائق</th>
                            <th>العميل</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% if (deliveries.length > 0) { %>
                            <% deliveries.forEach(delivery => { %>
                                <tr class="order-item"
                                    data-delivery-id="<%= delivery.id %>"
                                    data-order-id="<%= delivery.order.id %>"
                                    data-courier="<%= delivery.courier.name.toLowerCase() %>"
                                    data-customer="<%= (delivery.order.customer.name || '').toLowerCase() %>"
                                    data-status="<%= delivery.status %>"
                                    data-date="<%= new Date(delivery.createdAt).toISOString().slice(0,10) %>"
                                >
                                    <td><%= delivery.id %></td>
                                    <td>
                                        <a href="/admin/orders/<%= delivery.order.id %>">
                                            #<%= delivery.order.id %>
                                        </a>
                                    </td>
                                    <td>
                                        <a href="/admin/drivers/<%= delivery.courier.id %>/deliveries">
                                            <%= delivery.courier.name %>
                                        </a>
                                    </td>
                                    <td>
                                        <a href="/admin/customers/<%= delivery.order.customer.id %>">
                                            <%= delivery.order.customer.name || 'عميل غير معروف' %>
                                        </a>
                                    </td>
                                    <%
                                    let badgeClass = 'secondary';
                                    let statusText = 'غير معروف';

                                    switch (delivery.status) {
                                        case 'in_transit':
                                            badgeClass = 'info';
                                            statusText = 'قيد التوصيل';
                                            break;
                                        case 'delivered':
                                            badgeClass = 'success';
                                            statusText = 'تم التوصيل';
                                            break;
                                        case 'cancelled':
                                            badgeClass = 'danger';
                                            statusText = 'ملغي';
                                            break;
                                    }
                                    %>

                                    <td>
                                        <span class="badge bg-<%= badgeClass %>">
                                            <%= statusText %>
                                        </span>
                                    </td>
                                    <td><%= new Date(delivery.createdAt).toLocaleString('ar-EG') %></td>
                                    <td>
                                        <a href="/admin/deliveries/<%= delivery.order.id %>" class="btn btn-sm btn-info">
                                            تفاصيل
                                        </a>
                                        <a href="/admin/deliveries/<%= delivery.id %>/edit" class="btn btn-sm btn-primary">
                                            تعديل
                                        </a>
                                        <a href="/admin/deliveries/<%= delivery.id %>/delete" class="btn btn-sm btn-danger">
                                            حذف
                                        </a>
                                    </td>
                                </tr>
                            <% }) %>
                        <% } else { %>
                            <tr>
                                <td colspan="7" class="text-muted text-center">لا توجد توصيلات حالياً</td>
                            </tr>
                        <% } %>
                    </tbody>

                </table>
            </div>
        </div>
    </div>

    <% if (totalPages > 1) { %>
        <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
            <ul class="pagination">
                <% if (currentPage > 1) { %>
                    <li class="page-item">
                        <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
                    </li>
                <% } else { %>
                    <li class="page-item disabled">
                        <span class="page-link">السابق</span>
                    </li>
                <% } %>

                <% for(let i = 1; i <= totalPages; i++) { %>
                    <li class="page-item <%= currentPage === i ? 'active' : '' %>">
                        <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                    </li>
                <% } %>

                <% if (currentPage < totalPages) { %>
                    <li class="page-item">
                        <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
                    </li>
                <% } else { %>
                    <li class="page-item disabled">
                        <span class="page-link">التالي</span>
                    </li>
                <% } %>
            </ul>
        </nav>
    <% } %>
</div>
<script>
    // البحث والفلترة المباشرة للطلبات
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('searchInput').addEventListener('input', filterOrders);
    document.getElementById('statusFilter').addEventListener('change', filterOrders);
    document.getElementById('dateFilter').addEventListener('change', filterOrders);
});

function filterOrders() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const dateFilter = document.getElementById('dateFilter').value;

    const orders = document.querySelectorAll('.order-item');
    let visibleCount = 0;

    orders.forEach(order => {
        const deliveryId = order.dataset.deliveryId.toString().toLowerCase();
        const orderId = order.dataset.orderId.toString().toLowerCase();
        const courier = order.dataset.courier.toLowerCase();
        const customer = order.dataset.customer.toLowerCase();
        const status = order.dataset.status;
        const date = order.dataset.date;

        const matchesSearch = searchTerm === '' ||
                            deliveryId.includes(searchTerm) ||
                            orderId.includes(searchTerm) ||
                            courier.includes(searchTerm) ||
                            customer.includes(searchTerm);

        const matchesStatus = !statusFilter || status === statusFilter;
        const matchesDate = !dateFilter || date === dateFilter;

        if (matchesSearch && matchesStatus && matchesDate) {
            order.style.display = '';
            visibleCount++;
        } else {
            order.style.display = 'none';
        }
    });

    updateNoResultsMessage(visibleCount);
}

function updateNoResultsMessage(visibleCount) {
    let noResultsRow = document.getElementById('noResultsRow');

    if (visibleCount === 0) {
        if (!noResultsRow) {
            const tbody = document.querySelector('tbody');
            noResultsRow = document.createElement('tr');
            noResultsRow.id = 'noResultsRow';
            noResultsRow.innerHTML = `
                <td colspan="6" class="text-center py-4">
                    <div class="text-muted">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <p class="mb-0">لا توجد طلبات مطابقة لمعايير البحث</p>
                    </div>
                </td>
            `;
            tbody.appendChild(noResultsRow);
        }
        noResultsRow.style.display = '';
    } else {
        if (noResultsRow) {
            noResultsRow.style.display = 'none';
        }
    }
}

function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('dateFilter').value = '';
    filterOrders();
}
</script>