<!-- فلاتر مخصصة للعملاء -->

<!-- فلتر الحالة -->
<div class="col-md-3 mb-3">
    <label for="status" class="form-label">الحالة</label>
    <select class="form-control" id="status" name="status">
        <option value="">جميع الحالات</option>
        <option value="active" <%= (filters.status === 'active') ? 'selected' : '' %>>نشط</option>
        <option value="pending" <%= (filters.status === 'pending') ? 'selected' : '' %>>في الانتظار</option>
        <option value="inactive" <%= (filters.status === 'inactive') ? 'selected' : '' %>>غير نشط</option>
    </select>
</div>

<!-- فلتر تاريخ الإنشاء -->
<div class="col-md-3 mb-3">
    <label for="createdAt_from" class="form-label">تاريخ التسجيل من</label>
    <input type="date" 
           class="form-control" 
           id="createdAt_from" 
           name="createdAt_from" 
           value="<%= filters.createdAt_from || '' %>">
</div>

<div class="col-md-3 mb-3">
    <label for="createdAt_to" class="form-label">تاريخ التسجيل إلى</label>
    <input type="date" 
           class="form-control" 
           id="createdAt_to" 
           name="createdAt_to" 
           value="<%= filters.createdAt_to || '' %>">
</div>

<!-- فلتر عدد الطلبات -->
<div class="col-md-3 mb-3">
    <label for="ordersCount" class="form-label">عدد الطلبات</label>
    <select class="form-control" id="ordersCount" name="ordersCount">
        <option value="">جميع العملاء</option>
        <option value="0" <%= (filters.ordersCount === '0') ? 'selected' : '' %>>بدون طلبات</option>
        <option value="1-5" <%= (filters.ordersCount === '1-5') ? 'selected' : '' %>>1-5 طلبات</option>
        <option value="6-20" <%= (filters.ordersCount === '6-20') ? 'selected' : '' %>>6-20 طلب</option>
        <option value="20+" <%= (filters.ordersCount === '20+') ? 'selected' : '' %>>أكثر من 20 طلب</option>
    </select>
</div>
