<div class="auth-card">
    <div class="auth-header">
        <h1>
            <i class="fas fa-shield-alt me-2"></i>
            تسجيل دخول الإدارة
        </h1>
        <p>نظام إدارة المتجر المتطور</p>
    </div>

    <div class="auth-body">
        <% if (locals.error && typeof error === 'string' && error.trim().length > 0) { %>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <%= error %>
            </div>
        <% } %>

        <% if (locals.success && typeof success === 'string' && success.trim().length > 0) { %>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <%= success %>
            </div>
        <% } %>

        <form action="/admin/auth/login" method="POST">
            <div class="form-floating">
                <input type="text" class="form-control" id="username" name="username" placeholder="اسم المستخدم" required>
                <label for="username">
                    <i class="fas fa-user me-2"></i>
                    اسم المستخدم
                </label>
            </div>

            <div class="form-floating">
                <input type="password" class="form-control" id="password" name="password" placeholder="كلمة المرور" required>
                <label for="password">
                    <i class="fas fa-lock me-2"></i>
                    كلمة المرور
                </label>
            </div>

            <button type="submit" class="btn btn-login">
                <i class="fas fa-sign-in-alt me-2"></i>
                تسجيل الدخول
            </button>
        </form>
    </div>

    <div class="auth-footer">
        <i class="fas fa-copyright me-1"></i>
        جميع الحقوق محفوظة - نظام إدارة المتجر
    </div>
</div>