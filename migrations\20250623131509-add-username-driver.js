'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('DeliveryPeople', 'username', {
      type: Sequelize.STRING(100),
      allowNull: false,
      comment: 'اسم المستخدم'
    });

    await queryInterface.addColumn('DeliveryPeople', 'password', {
      type: Sequelize.STRING(100),
      allowNull: false,
      comment: 'كلمة المرور'
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('DeliveryPeople', 'username');
    await queryInterface.removeColumn('DeliveryPeople', 'password');
  }
};
