<!-- فلاتر مخصصة للمنتجات -->

<!-- فلتر الفئة -->
<div class="col-md-3 mb-3">
    <label for="category" class="form-label">الفئة</label>
    <select class="form-control" id="category" name="category">
        <option value="">جميع الفئات</option>
        <option value="electronics" <%= (filters.category === 'electronics') ? 'selected' : '' %>>إلكترونيات</option>
        <option value="clothing" <%= (filters.category === 'clothing') ? 'selected' : '' %>>ملابس</option>
        <option value="food" <%= (filters.category === 'food') ? 'selected' : '' %>>طعام</option>
        <option value="books" <%= (filters.category === 'books') ? 'selected' : '' %>>كتب</option>
        <option value="home" <%= (filters.category === 'home') ? 'selected' : '' %>>منزل</option>
        <option value="sports" <%= (filters.category === 'sports') ? 'selected' : '' %>>رياضة</option>
        <option value="beauty" <%= (filters.category === 'beauty') ? 'selected' : '' %>>جمال</option>
        <option value="toys" <%= (filters.category === 'toys') ? 'selected' : '' %>>ألعاب</option>
        <option value="automotive" <%= (filters.category === 'automotive') ? 'selected' : '' %>>سيارات</option>
        <option value="other" <%= (filters.category === 'other') ? 'selected' : '' %>>أخرى</option>
    </select>
</div>

<!-- فلتر الحالة -->
<div class="col-md-3 mb-3">
    <label for="status" class="form-label">الحالة</label>
    <select class="form-control" id="status" name="status">
        <option value="">جميع الحالات</option>
        <option value="active" <%= (filters.status === 'active') ? 'selected' : '' %>>نشط</option>
        <option value="inactive" <%= (filters.status === 'inactive') ? 'selected' : '' %>>غير نشط</option>
        <option value="out_of_stock" <%= (filters.status === 'out_of_stock') ? 'selected' : '' %>>نفد المخزون</option>
    </select>
</div>

<!-- فلتر السعر -->
<div class="col-md-3 mb-3">
    <label for="price_min" class="form-label">السعر من</label>
    <input type="number" 
           class="form-control" 
           id="price_min" 
           name="price_min" 
           value="<%= filters.price_min || '' %>"
           placeholder="0"
           min="0"
           step="0.01">
</div>

<div class="col-md-3 mb-3">
    <label for="price_max" class="form-label">السعر إلى</label>
    <input type="number" 
           class="form-control" 
           id="price_max" 
           name="price_max" 
           value="<%= filters.price_max || '' %>"
           placeholder="1000"
           min="0"
           step="0.01">
</div>

<!-- فلتر الكمية -->
<div class="col-md-3 mb-3">
    <label for="quantity_min" class="form-label">الكمية من</label>
    <input type="number" 
           class="form-control" 
           id="quantity_min" 
           name="quantity_min" 
           value="<%= filters.quantity_min || '' %>"
           placeholder="0"
           min="0">
</div>

<div class="col-md-3 mb-3">
    <label for="quantity_max" class="form-label">الكمية إلى</label>
    <input type="number" 
           class="form-control" 
           id="quantity_max" 
           name="quantity_max" 
           value="<%= filters.quantity_max || '' %>"
           placeholder="100"
           min="0">
</div>

<!-- فلتر تاريخ الإنشاء -->
<div class="col-md-3 mb-3">
    <label for="createdAt_from" class="form-label">تاريخ الإضافة من</label>
    <input type="date" 
           class="form-control" 
           id="createdAt_from" 
           name="createdAt_from" 
           value="<%= filters.createdAt_from || '' %>">
</div>

<div class="col-md-3 mb-3">
    <label for="createdAt_to" class="form-label">تاريخ الإضافة إلى</label>
    <input type="date" 
           class="form-control" 
           id="createdAt_to" 
           name="createdAt_to" 
           value="<%= filters.createdAt_to || '' %>">
</div>
