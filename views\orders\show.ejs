<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Order Details #<%= order.id %></h1>
        <a href="/orders" class="btn btn-secondary">Back to Orders</a>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Order Information</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Status:</strong>
                            <span class="badge bg-<%= order.status === 'pending' ? 'warning' : 
                                                    order.status === 'completed' ? 'success' : 
                                                    order.status === 'cancelled' ? 'danger' : 'secondary' %>">
                                <%= order.status.charAt(0).toUpperCase() + order.status.slice(1) %>
                            </span>
                        </div>
                        <div class="col-md-4">
                            <strong>Total Price:</strong>
                            $<%= order.totalPrice.toFixed(2) %>
                        </div>
                        <div class="col-md-4">
                            <strong>Created:</strong>
                            <%= new Date(order.createdAt).toLocaleDateString() %>
                        </div>
                    </div>

                    <% if (order.notes) { %>
                        <div class="mb-3">
                            <strong>Notes:</strong>
                            <p class="mb-0"><%= order.notes %></p>
                        </div>
                    <% } %>

                    <div class="mb-3">
                        <strong>Rating:</strong>
                        <% if (order.rating) { %>
                            <div>
                                <% for(let i = 0; i < order.rating; i++) { %>
                                    <i class="fas fa-star text-warning"></i>
                                <% } %>
                                <% for(let i = order.rating; i < 5; i++) { %>
                                    <i class="far fa-star text-warning"></i>
                                <% } %>
                            </div>
                        <% } else { %>
                            <p class="mb-0">Not Rated</p>
                        <% } %>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Order Items</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Quantity</th>
                                    <th>Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% order.orderDetails.forEach(detail => { %>
                                    <tr>
                                        <td><%= detail.product.name %></td>
                                        <td><%= detail.quantity %></td>
                                        <td>$<%= (detail.totalPrice / detail.quantity).toFixed(2) %></td>
                                        <td>$<%= detail.totalPrice.toFixed(2) %></td>
                                    </tr>
                                <% }); %>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="3" class="text-end"><strong>Total:</strong></td>
                                    <td><strong>$<%= order.totalPrice.toFixed(2) %></strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 