const bcrypt = require('bcryptjs');
const { User } = require('../models');
const logger = require('../utils/logger');

/**
 * Update admin user details
 */
async function updateAdmin(email, newData) {
    try {
        const admin = await User.findOne({ where: { email, role: 'admin' } });
        if (!admin) {
            throw new Error('Admin user not found');
        }

        if (newData.password) {
            newData.password = await bcrypt.hash(newData.password, 10);
        }

        await admin.update(newData);
        logger.info(`Admin user ${email} updated successfully`);
        return admin;
    } catch (error) {
        logger.error('Error updating admin:', error);
        throw error;
    }
}

/**
 * Reset admin password
 */
async function resetAdminPassword(email, newPassword) {
    try {
        const admin = await User.findOne({ where: { email, role: 'admin' } });
        if (!admin) {
            throw new Error('Admin user not found');
        }

        const hashedPassword = await bcrypt.hash(newPassword, 10);
        await admin.update({ password: hashedPassword });
        logger.info(`Admin password reset for ${email}`);
        return true;
    } catch (error) {
        logger.error('Error resetting admin password:', error);
        throw error;
    }
}

/**
 * Generate password hash
 */
async function generatePasswordHash(password) {
    try {
        const salt = await bcrypt.genSalt(10);
        const hash = await bcrypt.hash(password, salt);
        return hash;
    } catch (error) {
        logger.error('Error generating password hash:', error);
        throw error;
    }
}

module.exports = {
    updateAdmin,
    resetAdminPassword,
    generatePasswordHash
}; 