// Firebase Configuration
// This file centralizes all Firebase settings to ensure consistency

const firebaseConfig = {
  apiKey: "AIzaSyBdtHd9YCMgqvBs0KAVaen3Jg4M6x2mdhE",
  authDomain: "company-firebase-77daf.firebaseapp.com",
  projectId: "company-firebase-77daf",
  storageBucket: "company-firebase-77daf.appspot.com", // Consistent URL format
  messagingSenderId: "***********",
  appId: "1:***********:web:d74b24b187a1abf392fe95",
  measurementId: "G-YRPEE91G0C"
};

// VAPID Key for Web Push
const vapidKey = 'BBH3EJRa-TWHRVwbD1dmtBbmdfzf3SMi-NA8Zey9jW5LM-m5o5mTNUqTkiKMu6Iwn9dvrhEA69f0ZdL_gPuoJVc';

// Firebase Admin SDK Service Account Path
const serviceAccountPath = './company-firebase-77daf-firebase-adminsdk-fbsvc-9ccc834644.json';

// Notification settings
const notificationSettings = {
  // Default notification options
  defaultOptions: {
    icon: '/logo.png',
    badge: '/logo.png',
    requireInteraction: false,
    silent: false
  },
  
  // Android specific settings
  android: {
    priority: 'high',
    notification: {
      sound: 'default',
      channelId: 'default'
    }
  },
  
  // iOS/APNS specific settings
  apns: {
    payload: {
      aps: {
        sound: 'default',
        badge: 1
      }
    }
  },
  
  // Web Push specific settings
  webpush: {
    notification: {
      icon: '/logo.png',
      badge: '/logo.png',
      requireInteraction: true
    }
  }
};

// Error codes that indicate invalid tokens (should be deleted)
const invalidTokenErrorCodes = [
  'messaging/invalid-argument',
  'messaging/invalid-registration-token',
  'messaging/registration-token-not-registered',
  'messaging/mismatched-credential'
];

// Retry settings for failed notifications
const retrySettings = {
  maxRetries: 3,
  retryDelay: 1000, // milliseconds
  backoffMultiplier: 2
};

module.exports = {
  firebaseConfig,
  vapidKey,
  serviceAccountPath,
  notificationSettings,
  invalidTokenErrorCodes,
  retrySettings
};
