<div class="container" dir="rtl">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>التصنيفات</h1>
        <a href="/admin/categories/create" class="btn btn-primary">إضافة تصنيف جديد</a>
    </div>

    <!-- البحث والفلترة المباشرة -->
    <div class="row mb-4">
        <div class="col-md-6">
            <input type="text" class="form-control" placeholder="البحث في التصنيفات..." id="searchInput">
        </div>
        <div class="col-md-4">
            <select class="form-select" id="statusFilter">
                <option value="">جميع الحالات</option>
                <option value="active">نشط</option>
                <option value="inactive">غير نشط</option>
            </select>
        </div>
        <div class="col-md-2">
            <button class="btn btn-secondary w-100" onclick="clearFilters()">
                <i class="fas fa-times"></i> مسح
            </button>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped text-end">
            <thead>
                <tr>
                    <th>الرقم</th>
                    <th>الصورة</th>
                    <th>الاسم</th>
                    <th>الوصف</th>
                    <th>عدد المنتجات</th>
                    <th>الحالة</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <% categories.forEach(category => { %>
                    <tr class="category-item"
                        data-name="<%= category.name %>"
                        data-description="<%= category.description || '' %>"
                        data-status="<%= category.status %>"
                        data-products="<%= category.products ? category.products.length : 0 %>">
                        <td><%= category.id %></td>
                        <td>
                            <% if (category.image) { %>
                                <img src="<%= category.image %>" alt="Category Image" width="60" height="60" style="object-fit: cover;">
                            <% } else { %>
                                <span class="text-muted">لا يوجد</span>
                            <% } %>
                        </td> 
                        <td><%= category.name %></td>
                        <td><%= category.description %></td>
                        
                        <td><a href="/admin/products?category=<%= category.id %>"><%= category.products ? category.products.length : 0 %></a></td>
                        <td>
                            <span class="badge bg-<%= category.status === 'active' ? 'success' : 'secondary' %>">
                                <%= category.status === 'active' ? 'نشط' : 'غير نشط' %>
                            </span>
                        </td>
                        <td><%= new Date(category.createdAt).toLocaleString('ar-EG') %></td>
                        <td>
                            
                            <a href="/admin/categories/<%= category.id %>/edit" class="btn btn-sm btn-warning">تعديل</a>
                            <form action="/admin/categories/<%= category.id %>/delete" method="POST" class="d-inline">
                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من الحذف؟')">حذف</button>
                            </form>
                        </td>
                    </tr>
                <% }); %>
            </tbody>
        </table>
    </div>

    <% if (pagination.totalPages > 1) { %>
        <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
            <ul class="pagination">
                <% if (pagination.currentPage > 1) { %>
                    <li class="page-item">
                        <a class="page-link" href="?page=<%= pagination.currentPage - 1 %>">السابق</a>
                    </li>
                <% } else { %>
                    <li class="page-item disabled">
                        <span class="page-link">السابق</span>
                    </li>
                <% } %>

            
                <% for(let i = 1; i <= pagination.totalPages; i++) { %>
                    <li class="page-item <%= pagination.currentPage === i ? 'active' : '' %>">
                        <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                    </li>
                <% } %>

                <% if (pagination.currentPage < pagination.totalPages) { %>
                    <li class="page-item">
                        <a class="page-link" href="?page=<%= pagination.currentPage + 1 %>">التالي</a>
                    </li>
                <% } else { %>
                    <li class="page-item disabled">
                        <span class="page-link">التالي</span>
                    </li>
                <% } %>
            </ul>
        </nav>
    <% } %>

    <% if (categories.length === 0) { %>
        <div class="alert alert-info text-center">لا توجد تصنيفات.</div>
    <% } %>
</div>

<script>
    // البحث والفلترة المباشرة للتصنيفات
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('searchInput').addEventListener('input', filterCategories);
        document.getElementById('statusFilter').addEventListener('change', filterCategories);
    });

    function filterCategories() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const statusFilter = document.getElementById('statusFilter').value;

        const categories = document.querySelectorAll('.category-item');
        let visibleCount = 0;

        categories.forEach(category => {
            const name = category.dataset.name.toLowerCase();
            const description = category.dataset.description.toLowerCase();
            const status = category.dataset.status;

            const matchesSearch = searchTerm === '' ||
                                name.includes(searchTerm) ||
                                description.includes(searchTerm);
            const matchesStatus = !statusFilter || status === statusFilter;

            if (matchesSearch && matchesStatus) {
                category.style.display = '';
                visibleCount++;
            } else {
                category.style.display = 'none';
            }
        });

        // إظهار رسالة عدم وجود نتائج
        updateNoResultsMessage(visibleCount);
    }

    function updateNoResultsMessage(visibleCount) {
        let noResultsRow = document.getElementById('noResultsRow');

        if (visibleCount === 0) {
            if (!noResultsRow) {
                const tbody = document.querySelector('tbody');
                noResultsRow = document.createElement('tr');
                noResultsRow.id = 'noResultsRow';
                noResultsRow.innerHTML = `
                    <td colspan="8" class="text-center py-4">
                        <div class="text-muted">
                            <i class="fas fa-search fa-3x mb-3"></i>
                            <p class="mb-0">لا توجد تصنيفات مطابقة لمعايير البحث</p>
                        </div>
                    </td>
                `;
                tbody.appendChild(noResultsRow);
            }
            noResultsRow.style.display = '';
        } else {
            if (noResultsRow) {
                noResultsRow.style.display = 'none';
            }
        }
    }

    function clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('statusFilter').value = '';
        filterCategories();
    }
</script>
