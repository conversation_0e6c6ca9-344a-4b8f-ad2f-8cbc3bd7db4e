<!-- فلاتر مخصصة للمتاجر -->

<!-- فلتر الحالة -->
<div class="col-md-3 mb-3">
    <label for="status" class="form-label">الحالة</label>
    <select class="form-control" id="status" name="status">
        <option value="">جميع الحالات</option>
        <option value="active" <%= (filters.status === 'active') ? 'selected' : '' %>>نشط</option>
        <option value="pending" <%= (filters.status === 'pending') ? 'selected' : '' %>>في الانتظار</option>
        <option value="inactive" <%= (filters.status === 'inactive') ? 'selected' : '' %>>غير نشط</option>
        <option value="banned" <%= (filters.status === 'banned') ? 'selected' : '' %>>محظور</option>
    </select>
</div>

<!-- فلتر المنطقة -->
<div class="col-md-3 mb-3">
    <label for="areaId" class="form-label">المنطقة</label>
    <select class="form-control" id="areaId" name="areaId">
        <option value="">جميع المناطق</option>
        <% if (typeof areas !== 'undefined' && areas) { %>
            <% areas.forEach(area => { %>
                <option value="<%= area.id %>" <%= (filters.areaId == area.id) ? 'selected' : '' %>>
                    <%= area.name %>
                </option>
            <% }) %>
        <% } %>
    </select>
</div>

<!-- فلتر الفئة -->
<div class="col-md-3 mb-3">
    <label for="categoryId" class="form-label">الفئة</label>
    <select class="form-control" id="categoryId" name="categoryId">
        <option value="">جميع الفئات</option>
        <% if (typeof categories !== 'undefined' && categories) { %>
            <% categories.forEach(category => { %>
                <option value="<%= category.id %>" <%= (filters.categoryId == category.id) ? 'selected' : '' %>>
                    <%= category.name %>
                </option>
            <% }) %>
        <% } %>
    </select>
</div>

<!-- فلتر تاريخ الإنشاء -->
<div class="col-md-3 mb-3">
    <label for="createdAt_from" class="form-label">تاريخ الإنشاء من</label>
    <input type="date" 
           class="form-control" 
           id="createdAt_from" 
           name="createdAt_from" 
           value="<%= filters.createdAt_from || '' %>">
</div>

<div class="col-md-3 mb-3">
    <label for="createdAt_to" class="form-label">تاريخ الإنشاء إلى</label>
    <input type="date" 
           class="form-control" 
           id="createdAt_to" 
           name="createdAt_to" 
           value="<%= filters.createdAt_to || '' %>">
</div>

<!-- فلتر عدد الطلبات -->
<div class="col-md-3 mb-3">
    <label for="ordersCount" class="form-label">عدد الطلبات</label>
    <select class="form-control" id="ordersCount" name="ordersCount">
        <option value="">جميع المتاجر</option>
        <option value="0" <%= (filters.ordersCount === '0') ? 'selected' : '' %>>بدون طلبات</option>
        <option value="1-10" <%= (filters.ordersCount === '1-10') ? 'selected' : '' %>>1-10 طلبات</option>
        <option value="11-50" <%= (filters.ordersCount === '11-50') ? 'selected' : '' %>>11-50 طلب</option>
        <option value="50+" <%= (filters.ordersCount === '50+') ? 'selected' : '' %>>أكثر من 50 طلب</option>
    </select>
</div>

<!-- فلتر عدد المنتجات -->
<div class="col-md-3 mb-3">
    <label for="productsCount" class="form-label">عدد المنتجات</label>
    <select class="form-control" id="productsCount" name="productsCount">
        <option value="">جميع المتاجر</option>
        <option value="0" <%= (filters.productsCount === '0') ? 'selected' : '' %>>بدون منتجات</option>
        <option value="1-10" <%= (filters.productsCount === '1-10') ? 'selected' : '' %>>1-10 منتجات</option>
        <option value="11-50" <%= (filters.productsCount === '11-50') ? 'selected' : '' %>>11-50 منتج</option>
        <option value="50+" <%= (filters.productsCount === '50+') ? 'selected' : '' %>>أكثر من 50 منتج</option>
    </select>
</div>
