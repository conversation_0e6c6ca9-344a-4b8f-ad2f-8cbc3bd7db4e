<div class="container mt-4">
  <h2 class="mb-4">تفاصيل التوصيل للطلب رقم <span class="text-primary">#<%= order.id %></span></h2>

  <!-- معلومات الطلب -->
  <div class="card mb-4">
    <div class="card-header bg-light">
      <strong>معلومات الطلب</strong>
    </div>
    <div class="card-body">
      <div class="row mb-2">
        <div class="col-md-4"><strong>العميل:</strong> <%= order.customer?.name || 'غير متوفر' %></div>
        <div class="col-md-4"><strong>رقم الهاتف:</strong> <%= order.customer?.phoneNumber || 'غير متوفر' %></div>
        <div class="col-md-4"><strong>المجموع:</strong> <%= order.totalPrice.toLocaleString() %> ل.س</div>
      </div>
      <div class="row">
        <div class="col-md-4">
          <strong>حالة الطلب:</strong>
          <span class="badge bg-info text-dark"><%= order.status %></span>
        </div>
      </div>
    </div>
  </div>

  <!-- معلومات التوصيل -->
  <div class="card mb-4">
    <div class="card-header bg-light">
      <strong>معلومات التوصيل</strong>
    </div>
    <div class="card-body">
      <% if (delivery) { %>
        <div class="row mb-2">
          <div class="col-md-4"><strong>السائق:</strong> <%= delivery.courier?.name || 'غير محدد' %></div>
          <div class="col-md-4">
            <strong>حالة التوصيل:</strong>
            <% 
              let badgeClass = 'secondary';
              let statusText = delivery.status;

              switch (delivery.status) {
                case 'in_transit': badgeClass = 'info'; statusText = 'قيد التوصيل'; break;
                case 'delivered': badgeClass = 'success'; statusText = 'تم التوصيل'; break;
                case 'cancelled': badgeClass = 'danger'; statusText = 'ملغي'; break;
              }
            %>
            <span class="badge bg-<%= badgeClass %>"><%= statusText %></span>
          </div>
          <div class="col-md-4"><strong>تاريخ الإنشاء:</strong> <%= delivery.createdAt.toLocaleString('ar-EG') %></div>
        </div>
      <% } else { %>
        <p class="text-muted">لا توجد معلومات توصيل متاحة لهذا الطلب.</p>
      <% } %>
    </div>
  </div>

  <div class="text-center">
    <a href="/admin/orders/<%= order.id %>" class="btn btn-outline-secondary">رجوع إلى تفاصيل الطلب</a>
  </div>
</div>
