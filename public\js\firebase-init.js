/**
 * Firebase Initialization - Professional JavaScript Organization
 * Handles Firebase setup and messaging configuration
 */

// Firebase Configuration
const firebaseConfig = {
    apiKey: "AIzaSyBdtHd9YCMgqvBs0KAVaen3Jg4M6x2mdhE",
    authDomain: "company-firebase-77daf.firebaseapp.com",
    projectId: "company-firebase-77daf",
    storageBucket: "company-firebase-77daf.appspot.com",
    messagingSenderId: "76415949840",
    appId: "1:76415949840:web:d74b24b187a1abf392fe95",
    measurementId: "G-YRPEE91G0C"
};

// VAPID Key for FCM
const VAPID_KEY = 'BBH3EJRa-TWHRVwbD1dmtBbmdfzf3SMi-NA8Zey9jW5LM-m5o5mTNUqTkiKMu6Iwn9dvrhEA69f0ZdL_gPuoJVc';

// Global variables
let messaging;
let isFirebaseInitialized = false;

/**
 * Initialize Firebase and messaging
 * @returns {object} Firebase messaging instance
 */
function initializeFirebase() {
    try {
        if (isFirebaseInitialized) {
            return messaging;
        }

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        messaging = firebase.messaging();
        
        isFirebaseInitialized = true;
        console.log("✅ تم تهيئة Firebase بنجاح");
        
        return messaging;
    } catch (error) {
        console.error("❌ فشل في تهيئة Firebase:", error);
        throw error;
    }
}

/**
 * Register service worker for Firebase messaging
 * @returns {Promise<ServiceWorkerRegistration>}
 */
async function registerServiceWorker() {
    try {
        // Check if service worker is supported
        if (!('serviceWorker' in navigator)) {
            throw new Error('Service Worker غير مدعوم في هذا المتصفح');
        }

        // Unregister existing service workers
        const existingRegistrations = await navigator.serviceWorker.getRegistrations();
        for (const registration of existingRegistrations) {
            if (registration.scope.includes('firebase-messaging-sw')) {
                await registration.unregister();
                console.log("🗑️ تم إلغاء تسجيل Service Worker قديم");
            }
        }

        // Register new service worker
        const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
            scope: '/',
            updateViaCache: 'none'
        });

        console.log("✅ تم تسجيل Service Worker بنجاح");
        await navigator.serviceWorker.ready;
        
        return registration;
    } catch (error) {
        console.error("❌ فشل تسجيل Service Worker:", error);
        throw error;
    }
}

/**
 * Request notification permission
 * @returns {Promise<string>} Permission status
 */
async function requestNotificationPermission() {
    try {
        // Check if notifications are supported
        if (!('Notification' in window)) {
            throw new Error('الإشعارات غير مدعومة في هذا المتصفح');
        }

        // Request permission
        const permission = await Notification.requestPermission();
        console.log("🔐 حالة إذن الإشعارات:", permission);
        
        return permission;
    } catch (error) {
        console.error("❌ فشل في طلب إذن الإشعارات:", error);
        throw error;
    }
}

/**
 * Get FCM token
 * @param {ServiceWorkerRegistration} registration - Service worker registration
 * @returns {Promise<string>} FCM token
 */
async function getFCMToken(registration) {
    try {
        if (!messaging) {
            throw new Error('Firebase messaging غير مهيأ');
        }

        const token = await messaging.getToken({
            vapidKey: VAPID_KEY,
            serviceWorkerRegistration: registration
        });

        if (!token) {
            throw new Error('لم يتم الحصول على توكن صالح');
        }

        console.log("✅ FCM Token:", token.substring(0, 20) + "...");
        return token;
    } catch (error) {
        console.error("❌ فشل في الحصول على FCM Token:", error);
        throw error;
    }
}

/**
 * Save token to server
 * @param {string} token - FCM token
 * @returns {Promise<object>} Server response
 */
async function saveTokenToServer(token) {
    try {
        const response = await fetch('/admin/savetoken', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ token })
        });

        if (!response.ok) {
            throw new Error(`فشل في حفظ التوكن بالسيرفر (${response.status})`);
        }

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.message || 'فشل في حفظ التوكن');
        }

        console.log("✅ تم حفظ التوكن بنجاح");
        return result;
    } catch (error) {
        console.error("❌ فشل في حفظ التوكن:", error);
        throw error;
    }
}

/**
 * Setup foreground message handler
 */
function setupForegroundMessageHandler() {
    if (!messaging) {
        console.warn("⚠️ Firebase messaging غير مهيأ");
        return;
    }

    messaging.onMessage((payload) => {
        console.log('📥 إشعار وارد داخل الصفحة:', payload);

        // Create notification element
        const notification = document.createElement('div');
        notification.classList.add('alert', 'alert-info', 'alert-dismissible', 'fade', 'show');
        notification.innerHTML = `
            <i class="fas fa-bell me-2"></i>
            <strong>${payload.notification.title}</strong><br>
            ${payload.notification.body}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Add to notification container
        const container = document.getElementById('admin-notifications');
        if (container) {
            container.appendChild(notification);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Update notification system if available
        if (window.adminNotificationSystem) {
            window.adminNotificationSystem.handleIncomingNotification(payload);
        }
    });
}

/**
 * Complete Firebase initialization process
 * @returns {Promise<string>} FCM token
 */
async function initializeFirebaseMessaging() {
    try {
        console.log("🔄 بدء تهيئة Firebase Messaging...");

        // Initialize Firebase
        initializeFirebase();

        // Check browser support
        if (!('Notification' in window)) {
            throw new Error('هذا المتصفح لا يدعم الإشعارات');
        }

        if (!('serviceWorker' in navigator)) {
            throw new Error('هذا المتصفح لا يدعم Service Workers');
        }

        // Request permission
        const permission = await requestNotificationPermission();
        if (permission !== 'granted') {
            throw new Error('تم رفض إذن الإشعارات');
        }

        // Register service worker
        const registration = await registerServiceWorker();

        // Get FCM token
        const token = await getFCMToken(registration);

        // Save token to server
        await saveTokenToServer(token);

        // Setup message handler
        setupForegroundMessageHandler();

        console.log("✅ تم تهيئة Firebase Messaging بنجاح");
        return token;

    } catch (error) {
        console.error("❌ فشل في تهيئة Firebase Messaging:", error);
        throw error;
    }
}

/**
 * Auto-initialize Firebase on page load
 */
window.addEventListener('load', async () => {
    try {
        // Only auto-initialize if Firebase is available
        if (typeof firebase !== 'undefined') {
            await initializeFirebaseMessaging();
        } else {
            console.warn("⚠️ Firebase غير محمل - تم تخطي التهيئة التلقائية");
        }
    } catch (error) {
        console.error("❌ خطأ في التهيئة التلقائية:", error);
    }
});

// Export functions for global access
window.initializeFirebaseMessaging = initializeFirebaseMessaging;
window.FIREBASE_CONFIG = firebaseConfig;
window.VAPID_KEY = VAPID_KEY;
