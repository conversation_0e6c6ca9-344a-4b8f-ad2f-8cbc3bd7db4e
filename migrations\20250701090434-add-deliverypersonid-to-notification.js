'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('notifications', 'deliveryPersonId', {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'deliverypeople',
          key: 'id'
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE'
    });

  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('notifications', 'deliveryPersonId');
  }
};
