const { Customer, Order, Product } = require('../models');

class DashboardController {
    async index(req, res) {
        try {
            const [
                customerCount,
                orderCount,
                productCount,
                recentOrders
            ] = await Promise.all([
                Customer.count(),
                Order.count(),
                Product.count(),
                Order.findAll({
                    limit: 5,
                    order: [['createdAt', 'DESC']],
                    include: [
                        { model: Customer, as: 'customer' }
                    ]
                })
            ]);

            res.render('dashboard', {
                customerCount,
                orderCount,
                productCount,
                recentOrders
            });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }
}

module.exports = new DashboardController(); 