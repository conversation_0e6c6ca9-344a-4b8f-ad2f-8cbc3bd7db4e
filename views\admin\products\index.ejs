<!-- صفحة إدارة المنتجات -->

<style>
    .product-card {
        background: var(--white);
        border-radius: 16px;
        box-shadow: var(--shadow);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: none;
        overflow: hidden;
        padding: 1rem;
    }

    .product-card:hover {
        transform: translateY(-5px) scale(1.01);
        box-shadow: var(--shadow-lg);
    }

    .product-image {
        width: 100px;
        height: 100px;
        border-radius: 12px;
        object-fit: cover;
        border: 2px solid var(--bg-light);
    }

    .btn-custom {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border: none;
        color: var(--white);
        padding: 0.5rem 1.2rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
    }

    .btn-custom:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow);
        color: var(--white);
    }

    .badge-custom {
        background: var(--primary-color);
        color: var(--white);
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.85rem;
    }

    .price-tag {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
        padding: 0.4rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .page-header {
        background: var(--white);
        padding: 2rem;
        border-radius: 16px;
        box-shadow: var(--shadow);
        margin-bottom: 2rem;
    }

    .stats-card {
       background: linear-gradient(135deg, #abc0ca, #056191);
        color: var(--white);
        padding: 1.5rem;
        border-radius: 15px;
        text-align: center;
        box-shadow: var(--shadow);
        transition: transform 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px) scale(1.02);
    }

    .search-box {
        background: var(--white);
        border: 2px solid var(--bg-light);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .search-box:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(178, 205, 156, 0.25);
    }

    .btn-sm {
        padding: 0.35rem 0.6rem;
        font-size: 0.85rem;
        border-radius: 6px;
    }

    .badge.bg-success,
    .badge.bg-secondary {
        border-radius: 15px;
        padding: 0.4rem 0.7rem;
        font-weight: 500;
        transition: background 0.3s ease;
    }

    .badge.bg-success:hover {
        background-color: #28a745 !important;
    }

    .badge.bg-secondary:hover {
        background-color: #6c757d !important;
    }

    @media (max-width: 768px) {
        .product-image {
            width: 80px;
            height: 80px;
        }
        .product-card {
            padding: 0.8rem;
        }
    }

    .pagination .page-item .page-link {
        border-radius: 8px;
        color: var(--primary-color);
        transition: all 0.3s ease;
    }
    .pagination .page-item .page-link:hover {
        background-color: var(--primary-color);
        color: white;
    }
    .pagination .page-item.active .page-link {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }
</style>

<!-- محتوى الصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-2">
                    <i class="fas fa-box text-primary me-2"></i>
                    إدارة المنتجات
                </h1>
                <p class="text-muted mb-0">إدارة وتنظيم منتجات المتجر</p>
            </div>
            <div>
                <a href="/admin/products/create" class="btn btn-custom">
                    <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                </a>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="h4 mb-1"><%= allproducts.length %></div>
                <div class="small">إجمالي المنتجات</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="h4 mb-1"><%= allproducts.filter(p => p.quantity > 0).length %></div>
                <div class="small">متوفر في المخزن</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="h4 mb-1"><%= allproducts.filter(p => p.quantity === 0).length %></div>
                <div class="small">نفد من المخزن</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="h4 mb-1"><%= allproducts.filter(p => p.status === 'active').length %></div>
                <div class="small">منتجات نشطة</div>
            </div>
        </div>
    </div>

    <!-- البحث والتصفية -->
    <div class="row mb-4">
        <div class="col-md-6">
            <input type="text" class="form-control search-box" placeholder="البحث في المنتجات..." id="searchInput">
        </div>
        <div class="col-md-3">
            <select class="form-select search-box" id="categoryFilter">
                <option value="">جميع الفئات</option>
                <% if (categories) { %>
                    <% categories.forEach(category => { %>
                        <option value="<%= category.id %>"><%= category.name %></option>
                    <% }); %>
                <% } %>
            </select>
        </div>
        <div class="col-md-3">
            <select class="form-select search-box" id="statusFilter">
                <option value="">جميع الحالات</option>
                <option value="active">نشط</option>
                <option value="inactive">غير نشط</option>
                <option value="out-of-stock">نفد من المخزن</option>
            </select>
        </div>
    </div>

    <!-- المنتجات -->
    <div class="row" id="productsContainer">
        <% if (products && products.length > 0) { %>
            <% products.forEach(product => { %>
                <div class="col-lg-4 col-md-6 mb-4 product-item"
                    data-name="<%= product.name %>"
                    data-category="<%= product.categoryId %>"
                    data-status="<%= product.isActive ? 'active' : 'inactive' %><%= product.stock === 0 ? ' out-of-stock' : '' %>">
                    <div class="product-card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-start mb-3">
                                <% if (product.images && product.images.length > 0) { %>
                                    <img src="<%= product.images[0].image %>" alt="<%= product.name %>" class="product-image me-3">
                                <% } else { %>
                                    <div class="product-image me-3 d-flex align-items-center justify-content-center bg-light">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                <% } %>
                                <div class="flex-grow-1">
                                    <h5 class="card-title mb-2"><%= product.name %></h5>
                                    <p class="card-text text-muted small mb-2"><%= product.description %></p>
                                    <div class="d-flex align-items-center justify-content-between">
                                        <span class="price-tag"><%= product.price %> ل.س</span>
                                        <span class="badge <%= product.isActive ? 'bg-success' : 'bg-secondary' %>">
                                            <%= product.isActive ? 'نشط' : 'غير نشط' %>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <div class="small text-muted">المبيعات</div>
                                    <div class="fw-bold text-primary"><%= product.salesCount || 0 %></div>
                                </div>
                                <div class="col-4">
                                    <div class="small text-muted">التقييم</div>
                                    <div class="fw-bold text-warning">
                                        <i class="fas fa-star"></i> <%= product.rating || 0 %>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex gap-2">
                                <a href="/admin/products/<%= product.id %>" class="btn btn-outline-primary btn-sm flex-fill">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </a>
                                <a href="/admin/products/<%= product.id %>/edit" class="btn btn-outline-success btn-sm flex-fill">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </a>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteProduct(<%= product.id %>)">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <a href="/admin/products/<%= product.id %>/change-image" class="btn btn-outline-warning btn-sm flex-fill">
                                    <i class="fas fa-star me-1"></i>إضافة عرض
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <% }); %>
        <% } else { %>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-box fa-5x text-muted mb-3"></i>
                    <h3 class="text-muted">لا توجد منتجات</h3>
                    <p class="text-muted">ابدأ بإضافة منتجات جديدة لمتجرك</p>
                    <a href="/admin/products/create" class="btn btn-custom">
                        <i class="fas fa-plus me-2"></i>إضافة أول منتج
                    </a>
                </div>
            </div>
        <% } %>
    </div>

    <!-- الباجينيشن -->
    <% if (products && products.length > 0) { %>
        <nav aria-label="صفحات المنتجات" class="d-flex justify-content-center mt-4">
            <ul class="pagination">
                <% for(let i = 1; i <= pagination.totalPages; i++) { %>
                    <li class="page-item <%= pagination.currentPage === i ? 'active' : '' %>">
                        <a class="page-link" href="?page=<%= i %>">
                            <%= i %>
                        </a>
                    </li>
                <% } %>
            </ul>
        </nav>
    <% } %>
</div>

<script>
    // البحث والفلترة
    document.getElementById('searchInput').addEventListener('input', filterProducts);
    document.getElementById('categoryFilter').addEventListener('change', filterProducts);
    document.getElementById('statusFilter').addEventListener('change', filterProducts);

    function filterProducts() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const categoryFilter = document.getElementById('categoryFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;

        const products = document.querySelectorAll('.product-item');

        products.forEach(product => {
            const name = product.dataset.name.toLowerCase();
            const category = product.dataset.category;
            const status = product.dataset.status;

            const matchesSearch = name.includes(searchTerm);
            const matchesCategory = !categoryFilter || category === categoryFilter;
            const matchesStatus = !statusFilter || status.includes(statusFilter);

            if (matchesSearch && matchesCategory && matchesStatus) {
                product.style.display = 'block';
            } else {
                product.style.display = 'none';
            }
        });
    }

    // حذف المنتج
    function deleteProduct(productId) {
        if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            fetch(`/admin/products/${productId}`, {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ أثناء حذف المنتج');
                }
            })
            .catch(error => {
                console.error(error);
                alert('حدث خطأ أثناء حذف المنتج');
            });
        }
    }
</script>
