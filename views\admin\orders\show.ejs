<div class="container my-5">
  <div class="card shadow rounded-4 p-4 border-0">
    <div class="card-body">
      <h2 class="mb-3 text-primary">
        <i class="fas fa-file-invoice me-2"></i> تفاصيل الطلب <span class="text-secondary">#<%= order.id %></span>
      </h2>

    <div class="row g-4 mb-4">
  <div class="col-lg-3 col-md-6">
    <div class="bg-light p-3 rounded-3 shadow-sm h-100">
      <strong class="text-secondary">
        <i class="fas fa-user me-1 text-primary"></i> الزبون:
      </strong>
      <div class="fs-5 text-dark mt-1">
        <%= order.customer ? order.customer.name : 'غير متوفر' %>
      </div>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="bg-light p-3 rounded-3 shadow-sm h-100">
      <strong class="text-secondary">
        <i class="fas fa-map-marker-alt me-1 text-danger"></i> العنوان:
      </strong>
      <div class="fs-6 text-dark mt-1">
        <%= order.customer ? order.customer.address : 'غير متوفر' %>
      </div>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="bg-light p-3 rounded-3 shadow-sm h-100 text-center">
      <span class="badge fs-6 bg-<%= order.status === 'pending' ? 'warning' : order.status === 'completed' ? 'success' : order.status === 'cancelled' ? 'danger' : 'secondary' %> px-3 py-2">
        <%= order.status === 'pending' ? 'قيد المعالجة' :
             order.status === 'processing' ? 'جاري التحضير' :
             order.status === 'rejected' ? 'مرفوض' :
             order.status === 'completed' ? 'مكتمل' :
             order.status === 'cancelled' ? 'ملغي' : 'غير معروف' %>
      </span>
      <div class="small text-muted mt-2">
        <i class="fas fa-clock me-1"></i> <%= new Date(order.createdAt).toLocaleString('ar-EG') %>
      </div>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="bg-light p-3 rounded-3 shadow-sm h-100">
      <strong class="text-secondary">
        <i class="fas fa-dollar-sign me-1 text-success"></i> السعر الإجمالي:
      </strong>
      <div class="fs-5 text-dark mt-1">
        $<%= order.totalPrice?.toFixed(2) || '0.00' %>
      </div>
    </div>
  </div>
</div>


      <div class="table-responsive shadow-sm rounded-4 overflow-hidden">
        <table class="table table-striped text-center align-middle mb-0">
          <thead class="table-primary">
            <tr>
              <th>#</th>
              <th>المنتج</th>
              <th>الكمية</th>
              <th>السعر الكلي</th>
              <th>ملاحظات</th>
            </tr>
          </thead>
          <tbody>
            <% order.orderDetails.forEach((detail, index) => { %>
              <tr>
                <td><%= index + 1 %></td>
                <td><%= detail.product ? detail.product.name : 'غير متوفر' %></td>
                <td><%= detail.quantity %></td>
                <td>$<%= detail.totalPrice?.toFixed(2) || '0.00' %></td>
                <td><%= detail.notes || '-' %></td>
              </tr>
            <% }) %>
          </tbody>
        </table>
      </div>

      <div class="mt-5 d-flex flex-wrap gap-3 justify-content-center">
        <a href="/admin/orders" class="btn btn-outline-secondary px-4 rounded-pill shadow">
          <i class="fas fa-arrow-left me-1"></i> العودة للطلبات
        </a>

        <% if (order.status === 'pending') { %>
          <a href="/admin/deliveries/create?orderId=<%= order.id %>" 
            class="btn btn-outline-warning px-4 rounded-pill shadow"
            title="تعيين سائق">
            <i class="fas fa-motorcycle me-1"></i> تعيين سائق
          </a>
          <button class="btn btn-success px-4 rounded-pill shadow"
                  onclick="approveOrder(<%= order.id %>)"
                  title="الموافقة على الطلب">
            <i class="fas fa-check me-1"></i> موافقة
          </button>
          <button class="btn btn-danger px-4 rounded-pill shadow"
                  onclick="rejectOrder(<%= order.id %>)"
                  title="رفض الطلب">
            <i class="fas fa-times me-1"></i> رفض
          </button>
        <% } %>
      </div>
    </div>
  </div>
</div>
<script>
function approveOrder(orderId) {
    if (confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')) {
        fetch(`/admin/orders/${orderId}/approve`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم الموافقة على الطلب بنجاح');
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الموافقة على الطلب');
        });
    }
}

function rejectOrder(orderId) {
    const reason = prompt('أدخل سبب الرفض:');
    if (reason) {
        fetch(`/admin/orders/${orderId}/reject`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم رفض الطلب');
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء رفض الطلب');
        });
    }
}

</script>
