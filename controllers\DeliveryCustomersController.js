const { Order, Delivery, Customer, Delivery<PERSON>erson, DeliveryPeopleArea, OrderDetail, Product } = require('../models');
const NotificationsFunction = require('../controllers/NotificationsFunction');
const { Op } = require('sequelize');

class DeliveryController {

  async getDelivery(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        status = null,
        startDate = null,
        endDate = null
      } = req.query;

      const offset = (page - 1) * limit;
      const driverId = req.driver.id;

      const whereOrder = {
        status: {
          [Op.in]: ['pending', 'processing', 'out_for_delivery']
        }
      };

      // فلترة التاريخ
      if (startDate || endDate) {
        whereOrder.createdAt = {};
        if (startDate) whereOrder.createdAt[Op.gte] = new Date(startDate);
        if (endDate) whereOrder.createdAt[Op.lte] = new Date(endDate);
      }

      const { count, rows: deliveries } = await Delivery.findAndCountAll({
        where: { deliveryPersonId: driverId },
        include: [
          {
            model: Order,
            as: 'order',
            where: whereOrder,
            attributes: [
              'id', 'totalPrice', 'status', 'deliveryAddress', 'notes', 'createdAt'
            ],
            include: [
              {
                model: OrderDetail,
                as: 'orderDetails',
                attributes: ['quantity', 'totalPrice', 'notes'],
                include: [
                  {
                    model: Product,
                    as: 'product',
                    attributes: ['id', 'name', 'price'],
                    include: [
                      {
                        model: Category,
                        as: 'category',
                        attributes: ['id', 'name']
                      }
                    ]
                  }
                ]
              },
              {
                model: Customer,
                as: 'customer',
                attributes: ['id', 'name', 'phoneNumber', 'latitude', 'longitude']
              }
            ]
          }
        ],
        attributes: ['id', 'orderId'],
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['createdAt', 'DESC']]
      });

      const formatted = deliveries.map(delivery => ({
        id: delivery.order.id,
        totalPrice: delivery.order.totalPrice,
        status: delivery.order.status,
        address: delivery.order.deliveryAddress,
        createdAt: delivery.order.createdAt,
        customer: {
          id: delivery.order.customer.id,
          name: delivery.order.customer.name,
          phoneNumber: delivery.order.customer.phoneNumber,
          latitude: delivery.order.customer.latitude,
          longitude: delivery.order.customer.longitude
        },
        ProductsOrder: delivery.order.orderDetails.map(detail => ({
          id: detail.product.id,
          name: detail.product.name,
          category: detail.product.category?.name,
          quantity: detail.quantity,
          price: detail.product.price,
          totalPrice: detail.totalPrice,
          notes: detail.notes
        }))
      }));

      res.json({
        success: true,
        message: 'تم جلب الطلبات المعلقة بنجاح',
        data: {
          orders: formatted,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(count / limit),
            totalItems: count,
            itemsPerPage: parseInt(limit)
          }
        }
      });

    } catch (error) {
      console.error('خطأ في جلب الطلبات المعلقة:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ في الخادم',
        data: null
      });
    }
  }

  async getDeliverycompleted(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        status = null,
        startDate = null,
        endDate = null
      } = req.query;

      const offset = (page - 1) * limit;
      const driverId = req.driver.id;

      const whereOrder = {
        status: {
          [Op.in]: ['completed', 'cancelled', 'rejected']
        }
      };

      if (startDate || endDate) {
        whereOrder.createdAt = {};
        if (startDate) whereOrder.createdAt[Op.gte] = new Date(startDate);
        if (endDate) whereOrder.createdAt[Op.lte] = new Date(endDate);
      }

      const { count, rows: deliveries } = await Delivery.findAndCountAll({
        where: { deliveryPersonId: driverId },
        include: [
          {
            model: Order,
            as: 'order',
            where: whereOrder,
            attributes: ['id', 'totalPrice', 'status', 'deliveryAddress', 'notes', 'createdAt'],
            include: [
              {
                model: OrderDetail,
                as: 'orderDetails',
                attributes: ['quantity', 'totalPrice', 'notes'],
                include: [
                  {
                    model: Product,
                    as: 'product',
                    attributes: ['id', 'name', 'price'],
                    include: [
                      {
                        model: Category,
                        as: 'category',
                        attributes: ['id', 'name']
                      }
                    ]
                  }
                ]
              },
              {
                model: Customer,
                as: 'customer',
                attributes: ['id', 'name', 'phoneNumber', 'latitude', 'longitude']
              }
            ]
          }
        ],
        attributes: ['id', 'orderId', 'deliveryTime'],
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['createdAt', 'DESC']]
      });

      const formatted = deliveries.map(delivery => ({
        id: delivery.order.id,
        totalPrice: delivery.order.totalPrice,
        status: delivery.order.status,
        address: delivery.order.deliveryAddress,
        createdAt: delivery.order.createdAt,
        deliveryTime: delivery.deliveryTime,
        customer: {
          id: delivery.order.customer.id,
          name: delivery.order.customer.name,
          phoneNumber: delivery.order.customer.phoneNumber,
          latitude: delivery.order.customer.latitude,
          longitude: delivery.order.customer.longitude
        },
        ProductsOrder: delivery.order.orderDetails.map(detail => ({
          id: detail.product.id,
          name: detail.product.name,
          category: detail.product.category?.name,
          quantity: detail.quantity,
          price: parseFloat(detail.product.price),
          totalPrice: parseFloat(detail.totalPrice),
          notes: detail.notes
        }))
      }));

      res.json({
        success: true,
        message: 'تم جلب الطلبات المكتملة بنجاح',
        data: {
          orders: formatted,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(count / limit),
            totalItems: count,
            itemsPerPage: parseInt(limit)
          }
        }
      });

    } catch (error) {
      console.error('خطأ في جلب الطلبات المكتملة:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ في الخادم',
        data: null
      });
    }
  }

  async getDeliveryDetails(req, res) {
    try {
      const driverId = req.driver.id;
      const deliveryId = req.params.id;

      const delivery = await Delivery.findOne({
        where: { deliveryPersonId: driverId, id: deliveryId },
        attributes: ['id', 'orderId', 'deliveryTime'],
        include: [
          {
            model: Order,
            as: 'order',
            attributes: ['id', 'totalPrice', 'status', 'deliveryAddress', 'notes', 'createdAt'],
            include: [
              {
                model: OrderDetail,
                as: 'orderDetails',
                attributes: ['quantity', 'totalPrice', 'notes'],
                include: [
                  {
                    model: Product,
                    as: 'product',
                    attributes: ['id', 'name', 'price'],
                    include: [
                      {
                        model: Category,
                        as: 'category',
                        attributes: ['id', 'name']
                      }
                    ]
                  }
                ]
              },
              {
                model: Customer,
                as: 'customer',
                attributes: ['id', 'name', 'phoneNumber', 'latitude', 'longitude']
              }
            ]
          }
        ]
      });

      if (!delivery) {
        return res.status(404).json({
          success: false,
          message: 'التوصيل غير موجود',
          data: null
        });
      }

      const formatted = {
        id: delivery.order.id,
        totalPrice: parseFloat(delivery.order.totalPrice),
        status: delivery.order.status,
        address: delivery.order.deliveryAddress,
        notes: delivery.order.notes,
        createdAt: delivery.order.createdAt,
        deliveryTime: delivery.deliveryTime,
        customer: {
          id: delivery.order.customer.id,
          name: delivery.order.customer.name,
          phoneNumber: delivery.order.customer.phoneNumber,
          latitude: delivery.order.customer.latitude,
          longitude: delivery.order.customer.longitude
        },
        ProductsOrder: delivery.order.orderDetails.map(detail => ({
          id: detail.product.id,
          name: detail.product.name,
          category: detail.product.category?.name,
          quantity: detail.quantity,
          price: parseFloat(detail.product.price),
          totalPrice: parseFloat(detail.totalPrice),
          notes: detail.notes
        }))
      };

      res.json({
        success: true,
        message: 'تم جلب تفاصيل الطلب بنجاح',
        data: formatted
      });

    } catch (error) {
      console.error('خطأ في جلب تفاصيل الطلب:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ في الخادم',
        data: null
      });
    }
  }
  
}

module.exports = new DeliveryController();
