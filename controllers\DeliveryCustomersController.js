const { Order, Delivery, Customer, Delivery<PERSON>erson, DeliveryPeopleArea, OrderDetail, Product,Category } = require('../models');
const NotificationsFunction = require('../controllers/NotificationsFunction');
const { Op } = require('sequelize');

class DeliveryController {
  
    async getDelivery(req, res) {
        try { 
            const {
                page = 1,
                limit = 10,
                status = null,
                startDate = null,
                endDate = null
            } = req.query;
            const offset = (page - 1) * limit;
            const driverId = req.driver.id;
            const deliveryIds = await Delivery.findAll({
                    where: { deliveryPersonId: driverId },
                    include: [
                        {
                            model: Order,
                            as: 'order',
                            where: {
                                status: {
                                    [Op.in]: ['pending', 'processing', 'out_for_delivery']
                                }
                            },
                            attributes: [], // لا تحتاج بيانات هنا
                        }
                    ],
                    attributes: ['id', 'orderId'],
                    limit: parseInt(limit),
                    offset: parseInt(offset),
                    order: [['createdAt', 'DESC']],
                });
                const orderIds = deliveryIds.map(d => d.orderId);

                const deliveries = await Delivery.findAll({
                    where: {
                        deliveryPersonId: driverId,
                        orderId: {
                            [Op.in]: orderIds
                        }
                    },
                    attributes: ['id', 'orderId'],
                    include: [
                        {
                            model: Order,
                            as: 'order',
                            attributes: ['totalPrice', 'status', 'deliveryAddress', 'notes','createdAt'],
                            include: [
                                {
                                    model: OrderDetail,
                                    as: 'orderDetails',
                                    attributes: ['quantity', 'totalPrice', 'notes'],
                                    include: [
                                        {
                                            model: Product,
                                            as: 'product',
                                            attributes: ['id', 'name', 'price'],
                                            include: [
                                                {
                                                    model: Category,
                                                    as: 'category',
                                                    attributes: ['id', 'name']
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    model: Customer,
                                    as: 'customer',
                                    attributes: ['id', 'name', 'phoneNumber', 'latitude', 'longitude']
                                }
                            ]
                        }
                    ],
                    order: [['createdAt', 'DESC']]
                });

        
            const formatted = deliveries.map(delivery => ({
                id: delivery.orderId,
                totalPrice: delivery.order.totalPrice,
                status: delivery.order.status,
                address: delivery.order.deliveryAddress,
                createdAt: delivery.order.createdAt,
                customer: {
                    id: delivery.order.customer.id,
                    name: delivery.order.customer.name,
                    phoneNumber: delivery.order.customer.phoneNumber,
                    latitude: delivery.order.customer.latitude,
                    longitude: delivery.order.customer.longitude
                },
                ProductsOrder: delivery.order.orderDetails.map(detail => ({
                    id: detail.product.id,
                    name: detail.product.name,
                    category: detail.product.category.name,
                    quantity: detail.quantity,
                    price: detail.product.price,
                    totalPrice: detail.totalPrice,
                    notes: detail.notes
                }))
            }));

            const count = await Delivery.count({
                where: {
                    deliveryPersonId: driverId
                }
            });


            res.json({
                success: true,
                message: 'تم جلب الطلبات المعلقة بنجاح',
                data: {
                    orders: formatted,
                   /* pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }*/
                  }
            });
        } catch (error) {
            console.error('خطأ في جلب الطلبات المعلقة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    async getDeliverycompleted(req, res) {
        try { 
            const {
                page = 1,
                limit = 10,
                status = null,
                startDate = null,
                endDate = null
            } = req.query;
            const offset = (page - 1) * limit;
            const driverId = req.driver.id;
            const deliveryIds = await Delivery.findAll({
                    where: { deliveryPersonId: driverId },
                    include: [
                        {
                            model: Order,
                            as: 'order',
                            where: {
                                status: {
                                    [Op.in]: ['completed', 'cancelled', 'rejected']
                                }
                            },
                            attributes: [], // لا تحتاج بيانات هنا
                        }
                    ],
                    attributes: ['id', 'orderId'],
                    limit: parseInt(limit),
                    offset: parseInt(offset),
                    order: [['createdAt', 'DESC']],
                });
                const orderIds = deliveryIds.map(d => d.orderId);

                const deliveries = await Delivery.findAll({
                    where: {
                        deliveryPersonId: driverId,
                        orderId: {
                            [Op.in]: orderIds
                        }
                    },
                    attributes: ['id', 'orderId','deliveryTime'],
                    include: [
                        {
                            model: Order,
                            as: 'order',
                            attributes: ['totalPrice', 'status', 'deliveryAddress', 'notes','createdAt'],
                            include: [
                                {
                                    model: OrderDetail,
                                    as: 'orderDetails',
                                    attributes: ['quantity', 'totalPrice', 'notes'],
                                    include: [
                                        {
                                            model: Product,
                                            as: 'product',
                                            attributes: ['id', 'name', 'price'],
                                            include: [
                                                {
                                                    model: Category,
                                                    as: 'category',
                                                    attributes: ['id', 'name']
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    model: Customer,
                                    as: 'customer',
                                    attributes: ['id', 'name', 'phoneNumber', 'latitude', 'longitude']
                                }
                            ]
                        }
                    ],
                    order: [['createdAt', 'DESC']]
                });

        
            const formatted = deliveries.map(delivery => ({
                id: delivery.orderId,
                totalPrice: delivery.order.totalPrice,
                status: delivery.order.status,
                address: delivery.order.deliveryAddress,
                createdAt: delivery.order.createdAt,
                deliveryTime: delivery.deliveryTime,
                customer: {
                    id: delivery.order.customer.id,
                    name: delivery.order.customer.name,
                    phoneNumber: delivery.order.customer.phoneNumber,
                    latitude: delivery.order.customer.latitude,
                    longitude: delivery.order.customer.longitude
                },
                ProductsOrder: delivery.order.orderDetails.map(detail => ({
                    id: detail.product.id,
                    name: detail.product.name,
                    category: detail.product.category.name,
                    quantity: detail.quantity,
                    price: detail.product.price,
                    totalPrice: detail.totalPrice,
                    notes: detail.notes
                }))
            }));

            const count = await Delivery.count({
                where: {
                    deliveryPersonId: driverId
                }
            });


            res.json({
                success: true,
                message: 'تم جلب الطلبات المعلقة بنجاح',
                data: {
                    orders: formatted,
                   /* pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }*/
                }
            });
        } catch (error) {
            console.error('خطأ في جلب الطلبات المعلقة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    async getDeliveryDetails(req, res) {
        try {
            const driverId = req.driver.id;
            const deliveryId = req.params.id;         
            const delivery = await Delivery.findAll({
                where: { deliveryPersonId: driverId, id: deliveryId },
                attributes:['id','orderId']
                ,include: [
                    {
                        model: Order,
                        as: 'order',
                        attributes: ['totalPrice', 'status','deliveryAddress','notes'],
                        include: [
                            {
                                model: OrderDetail,
                                as: 'orderDetails',
                                attributes: ['quantity', 'totalPrice','notes'],
                                include: [
                                    {
                                        model: Product,
                                        as: 'product',
                                        attributes: ['id', 'name', 'price'],
                                        include: [
                                            {
                                                model: Category,
                                                as: 'category',
                                                attributes: ['id', 'name']
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                model: Customer,
                                as: 'customer',
                                attributes: ['id', 'name','phoneNumber','latitude','longitude']
                            }
                        ]
                    }
                ]
            });
                        
            res.json({
                success: true,
                message: 'تم جلب تفاصيل الطلب بنجاح',
                data: delivery
            });
        } catch (error) {
            console.error('خطأ في جلب تفاصيل الطلب:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }
}

module.exports = new DeliveryController();
