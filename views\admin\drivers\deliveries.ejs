<h3>توصيلات السائق</h3>

<!-- البحث والفلترة المباشرة -->
<div class="row mb-4">
  <div class="col-md-6">
    <input type="text" class="form-control" placeholder="البحث في التوصيلات..." id="searchInput">
  </div>
  <div class="col-md-4">
    <select class="form-select" id="statusFilter">
      <option value="">جميع الحالات</option>
      <option value="pending">في الانتظار</option>
      <option value="in_transit">قيد التوصيل</option>
      <option value="delivered">تم التوصيل</option>
      <option value="cancelled">ملغي</option>
    </select>
  </div>
  <div class="col-md-2">
    <button class="btn btn-secondary w-100" onclick="clearFilters()">
      <i class="fas fa-times"></i> مسح
    </button>
  </div>
</div>

<table class="table table-striped">
  <thead>
    <tr>
      <th>#</th>
      <th>Status</th>
      <th>Pickup Time</th>
      <th>Delivery Time</th>
      <th>Notes</th>
    </tr>
  </thead>
  <tbody>
    <% deliveries.forEach((d, i) => { %>
      <tr class="delivery-item"
          data-status="<%= d.status %>"
          data-notes="<%= d.notes || '' %>"
          data-pickup="<%= d.pickupTime ? new Date(d.pickupTime).toISOString().split('T')[0] : '' %>"
          data-delivery="<%= d.deliveryTime ? new Date(d.deliveryTime).toISOString().split('T')[0] : '' %>">
        <td><%= i + 1 %></td>
        <td><%= d.status %></td>
        <td><%= d.pickupTime ? new Date(d.pickupTime).toLocaleString() : '-' %></td>
        <td><%= d.deliveryTime ? new Date(d.deliveryTime).toLocaleString() : '-' %></td>
        <td><%= d.notes || '-' %></td>
      </tr>
    <% }) %>
  </tbody>
</table>

<script>
  // البحث والفلترة المباشرة لتوصيلات السائق
  document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('searchInput').addEventListener('input', filterDeliveries);
    document.getElementById('statusFilter').addEventListener('change', filterDeliveries);
  });

  function filterDeliveries() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;

    const deliveries = document.querySelectorAll('.delivery-item');
    let visibleCount = 0;

    deliveries.forEach(delivery => {
      const status = delivery.dataset.status.toLowerCase();
      const notes = delivery.dataset.notes.toLowerCase();

      const matchesSearch = searchTerm === '' ||
                          status.includes(searchTerm) ||
                          notes.includes(searchTerm);
      const matchesStatus = !statusFilter || delivery.dataset.status === statusFilter;

      if (matchesSearch && matchesStatus) {
        delivery.style.display = '';
        visibleCount++;
      } else {
        delivery.style.display = 'none';
      }
    });

    // إظهار رسالة عدم وجود نتائج
    updateNoResultsMessage(visibleCount);
  }

  function updateNoResultsMessage(visibleCount) {
    let noResultsRow = document.getElementById('noResultsRow');

    if (visibleCount === 0) {
      if (!noResultsRow) {
        const tbody = document.querySelector('tbody');
        noResultsRow = document.createElement('tr');
        noResultsRow.id = 'noResultsRow';
        noResultsRow.innerHTML = `
          <td colspan="5" class="text-center py-4">
            <div class="text-muted">
              <i class="fas fa-search fa-3x mb-3"></i>
              <p class="mb-0">لا توجد توصيلات مطابقة لمعايير البحث</p>
            </div>
          </td>
        `;
        tbody.appendChild(noResultsRow);
      }
      noResultsRow.style.display = '';
    } else {
      if (noResultsRow) {
        noResultsRow.style.display = 'none';
      }
    }
  }

  function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    filterDeliveries();
  }
</script>
