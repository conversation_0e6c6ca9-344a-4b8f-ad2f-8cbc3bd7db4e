const express = require('express');
const router = express.Router();

const adminRouter = require('./admin');
const adminAuth = require('./admin-auth');
const customersRouter = require('./customers');
const healthRouter = require('./health');

// Home page 
router.get('/', (req, res) => {
    res.render('Home', { layout: 'layouts/auth' });
});

// Admin authentication
router.use('/admin/auth', adminAuth.router);

// Admin panel
router.use('/admin', adminRouter);

// Customers
router.use('/customers', customersRouter);

// Health check
router.use('/health', healthRouter);

module.exports = router;
