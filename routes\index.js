const express = require('express');
const router = express.Router();

const adminRouter = require('./admin');
const adminAuth = require('./admin-auth');
const customersRouter = require('./customers');
const customerNotificationsRouter = require('./customer-notifications');
const healthRouter = require('./health');

// Home page 
router.get('/', (req, res) => {
    res.render('Home', { layout: 'layouts/auth' });
});

// Admin authentication
router.use('/admin/auth', adminAuth.router);

// Admin panel
router.use('/admin', adminRouter);

// Customer notifications
router.use('/customers/notifications', customerNotificationsRouter);

// Customers
router.use('/customers', customersRouter);

// Health check
router.use('/health', healthRouter);

module.exports = router;
