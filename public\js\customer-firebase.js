/**
 * نظام إشعارات Firebase للعملاء
 * Customer Firebase Notification System
 */

class CustomerFirebaseNotifications {
    constructor() {
        this.messaging = null;
        this.isInitialized = false;
        this.vapidKey = 'BBH3EJRa-TWHRVwbD1dmtBbmdfzf3SMi-NA8Zey9jW5LM-m5o5mTNUqTkiKMu6Iwn9dvrhEA69f0ZdL_gPuoJVc';
        this.authToken = null;
    }

    /**
     * تهيئة نظام الإشعارات
     */
    async initialize(authToken) {
        try {
            this.authToken = authToken;
            
            if (!('serviceWorker' in navigator)) {
                throw new Error('Service Worker غير مدعوم في هذا المتصفح');
            }

            if (!('Notification' in window)) {
                throw new Error('الإشعارات غير مدعومة في هذا المتصفح');
            }

            // تهيئة Firebase
            if (typeof firebase === 'undefined') {
                throw new Error('Firebase SDK غير محمل');
            }

            // تهيئة Firebase Messaging
            this.messaging = getMessaging();
            
            // تسجيل Service Worker
            await this.registerServiceWorker();
            
            this.isInitialized = true;
            console.log('✅ تم تهيئة نظام إشعارات العملاء بنجاح');
            
            return true;
        } catch (error) {
            console.error('❌ فشل في تهيئة نظام الإشعارات:', error);
            throw error;
        }
    }

    /**
     * تسجيل Service Worker
     */
    async registerServiceWorker() {
        try {
            const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
                scope: '/',
                updateViaCache: 'none'
            });

            await navigator.serviceWorker.ready;
            console.log('✅ تم تسجيل Service Worker بنجاح');
            
            return registration;
        } catch (error) {
            console.error('❌ فشل في تسجيل Service Worker:', error);
            throw error;
        }
    }

    /**
     * طلب إذن الإشعارات وحفظ التوكن
     */
    async requestPermissionAndSaveToken() {
        try {
            if (!this.isInitialized) {
                throw new Error('يجب تهيئة النظام أولاً');
            }

            // طلب الإذن
            const permission = await Notification.requestPermission();
            
            if (permission !== 'granted') {
                throw new Error('تم رفض إذن الإشعارات');
            }

            // الحصول على التوكن
            const token = await this.messaging.getToken({
                vapidKey: this.vapidKey
            });

            if (!token) {
                throw new Error('فشل في الحصول على توكن الإشعارات');
            }

            // حفظ التوكن في الخادم
            await this.saveTokenToServer(token);
            
            // إعداد استقبال الرسائل
            this.setupMessageHandlers();
            
            console.log('✅ تم تفعيل الإشعارات وحفظ التوكن بنجاح');
            return token;
            
        } catch (error) {
            console.error('❌ فشل في تفعيل الإشعارات:', error);
            throw error;
        }
    }

    /**
     * حفظ التوكن في الخادم
     */
    async saveTokenToServer(token) {
        try {
            const deviceInfo = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                timestamp: new Date().toISOString()
            };

            const response = await fetch('/customers/save-firebase-token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authToken}`
                },
                body: JSON.stringify({
                    token: token,
                    deviceInfo: deviceInfo
                })
            });

            if (!response.ok) {
                throw new Error(`فشل في حفظ التوكن: ${response.status}`);
            }

            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.message || 'فشل في حفظ التوكن');
            }

            console.log('✅ تم حفظ التوكن في الخادم بنجاح');
            return result;
            
        } catch (error) {
            console.error('❌ فشل في حفظ التوكن:', error);
            throw error;
        }
    }

    /**
     * إعداد معالجات الرسائل
     */
    setupMessageHandlers() {
        try {
            // معالج الرسائل في المقدمة
          onMessage(this.messaging, (payload) => {
                console.log('📨 تم استلام إشعار:', payload);
                this.handleIncomingNotification(payload);
            });

            // معالج تحديث التوكن
            this.messaging.onTokenRefresh(async () => {
                try {
                    console.log('🔄 تحديث التوكن...');
                    const newToken = await this.messaging.getToken({
                        vapidKey: this.vapidKey
                    });
                    
                    if (newToken) {
                        await this.saveTokenToServer(newToken);
                        console.log('✅ تم تحديث التوكن بنجاح');
                    }
                } catch (error) {
                    console.error('❌ فشل في تحديث التوكن:', error);
                }
            });

            console.log('✅ تم إعداد معالجات الرسائل');
            
        } catch (error) {
            console.error('❌ فشل في إعداد معالجات الرسائل:', error);
        }
    }

    /**
     * عرض الإشعار
     */
    showNotification(notification, data = {}) {
        try {
            // إنشاء إشعار مخصص
            const notificationOptions = {
                body: notification.body,
                icon: '/images/logo.png',
                badge: '/images/badge.png',
                tag: data.type || 'customer-notification',
                requireInteraction: true,
                actions: [
                    {
                        action: 'view',
                        title: 'عرض التفاصيل'
                    },
                    {
                        action: 'close',
                        title: 'إغلاق'
                    }
                ],
                data: data
            };

            // عرض الإشعار
            if ('serviceWorker' in navigator && 'showNotification' in ServiceWorkerRegistration.prototype) {
                navigator.serviceWorker.ready.then(registration => {
                    registration.showNotification(notification.title, notificationOptions);
                });
            } else {
                new Notification(notification.title, notificationOptions);
            }

            // عرض toast notification أيضاً
            this.showToast(notification.title, notification.body, 'info');
            
        } catch (error) {
            console.error('❌ فشل في عرض الإشعار:', error);
        }
    }

    /**
     * عرض toast notification
     */
    showToast(title, message, type = 'info') {
        try {
            // إنشاء toast element
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'info' ? 'primary' : type} border-0`;
            toast.setAttribute('role', 'alert');
            toast.setAttribute('aria-live', 'assertive');
            toast.setAttribute('aria-atomic', 'true');
            
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <strong>${title}</strong><br>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;

            // إضافة إلى container
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            toastContainer.appendChild(toast);

            // تفعيل Toast
            if (typeof bootstrap !== 'undefined') {
                const bsToast = new bootstrap.Toast(toast, {
                    autohide: true,
                    delay: 5000
                });
                bsToast.show();

                // إزالة العنصر بعد الإخفاء
                toast.addEventListener('hidden.bs.toast', () => {
                    toast.remove();
                });
            } else {
                // fallback إذا لم يكن Bootstrap متاحاً
                setTimeout(() => {
                    toast.remove();
                }, 5000);
            }
            
        } catch (error) {
            console.error('❌ فشل في عرض Toast:', error);
        }
    }

    /**
     * التحقق من حالة الإشعارات
     */
    getNotificationStatus() {
        if (!('Notification' in window)) {
            return 'not-supported';
        }
        
        return Notification.permission;
    }

    /**
     * إلغاء تفعيل الإشعارات
     */
    async disableNotifications() {
        try {
            if (this.messaging) {
                await this.messaging.deleteToken();
                console.log('✅ تم إلغاء تفعيل الإشعارات');
            }
        } catch (error) {
            console.error('❌ فشل في إلغاء تفعيل الإشعارات:', error);
        }
    }
}

// إنشاء instance عام
window.CustomerNotifications = new CustomerFirebaseNotifications();
