'use strict';

const bcrypt = require('bcrypt');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // إضافة مدير تجريبي
    const hashedPassword = await bcrypt.hash('admin123', 10);

    await queryInterface.bulkInsert('Admins', [{
      name: 'مدير النظام',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'super_admin',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    }], {});

    // إضافة فئات تجريبية
    await queryInterface.bulkInsert('Categories', [
      {
        name: 'إلكترونيات',
        description: 'أجهزة إلكترونية ومعدات تقنية',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'ملابس',
        description: 'ملابس رجالية ونسائية وأطفال',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'منزل ومطبخ',
        description: 'أدوات منزلية ومطبخية',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ], {});

    // إضافة عميل تجريبي
    await queryInterface.bulkInsert('Customers', [{
      name: 'أحمد محمد',
      email: '<EMAIL>',
      phoneNumber: '0501234567',
      password: await bcrypt.hash('customer123', 10),
      status: 'active',
      discountRate: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    }], {});
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.bulkDelete('Admins', null, {});
    await queryInterface.bulkDelete('Categories', null, {});
    await queryInterface.bulkDelete('Customers', null, {});
  }
};
