'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // حذف العمود القديم
    await queryInterface.removeColumn('Customers', 'permissions');

    // إضافة العمود الجديد بنوع INTEGER
    await queryInterface.addColumn('Customers', 'permissions', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'صلاحيات العميل (0 = غير مفعل، 1 = مفعل)'
    });
  },

  async down (queryInterface, Sequelize) {
    // حذف العمود الجديد
    await queryInterface.removeColumn('Customers', 'permissions');

    // إضافة العمود القديم بنوع TEXT
    await queryInterface.addColumn('Customers', 'permissions', {
      type: Sequelize.TEXT,
      allowNull: true,
      defaultValue: null,
      comment: 'صلاحيات العميل (JSON format)'
    });
  }
};
