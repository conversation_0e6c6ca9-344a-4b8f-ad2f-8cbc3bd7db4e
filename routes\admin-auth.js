const express = require('express');
const router = express.Router();
const adminAuthController = require('../controllers/AdminAuthController');
const { auth } = require('../middleware/auth');

// Authentication middleware (use JWT auth)
const requireAdminAuth = auth.admin;

// Super admin middleware
const requireSuperAdmin = (req, res, next) => {
    if (req.user.role !== 'super_admin') {
        return res.status(403).render('error', {
            error: {
                status: 403,
                message: 'Access denied. Super admin privileges required.'
            }
        });
    }
    next();
};

// Login routes
router.get('/login', adminAuthController.showLogin);
router.post('/login', adminAuthController.login);
router.get('/logout', adminAuthController.logout);

// Password change routes (protected)
router.get('/change-password', requireAdminAuth, adminAuthController.showChangePassword);
router.post('/change-password', requireAdminAuth, adminAuthController.changePassword);


module.exports = {
    router,
    requireAdminAuth,
    requireSuperAdmin
};