# Store Management System

A professional management system built with Node.js, Express, and SQL Server.

## Features

- User authentication and authorization
- Product inventory management
- Order processing
- Customer management
- Area and country management
- File upload capabilities
- API documentation with Swagger
- Comprehensive error handling
- Logging system
- Security features (Helmet, CORS, Rate Limiting)

## Tech Stack

- **Backend**: Node.js, Express.js
- **Database**: SQL Server with Sequelize ORM
- **Template Engine**: EJS
- **Authentication**: Session-based with JWT support
- **File Upload**: express-fileupload
- **Security**: Helmet, CORS, Rate Limiting
- **Logging**: Winston
- **Documentation**: Swagger
- **Testing**: Jest
- **Code Quality**: ESLint, Prettier
- **TypeScript**: Support included

## Project Structure

```
store-management-system/
├── src/                    # Source code
│   ├── app.js             # Application entry point
│   ├── config/            # Configuration files
│   │   ├── database.js    # Database configuration
│   │   └── swagger.js     # API documentation
│   ├── controllers/       # Route controllers
│   ├── middleware/        # Custom middleware
│   │   ├── auth.js       # Authentication middleware
│   │   └── errorHandler.js # Error handling
│   ├── models/           # Database models
│   ├── routes/           # Route definitions
│   ├── services/         # Business logic
│   ├── utils/            # Utility functions
│   │   ├── logger.js     # Logging utility
│   │   └── validators.js # Input validation
│   └── views/            # EJS templates
├── logs/                 # Application logs
├── public/              # Static files
│   ├── css/            # Stylesheets
│   ├── js/             # Client-side JavaScript
│   └── uploads/        # Uploaded files
├── tests/              # Test files
├── migrations/         # Database migrations
├── .env.example       # Example environment variables
├── .eslintrc.js      # ESLint configuration
├── .prettierrc       # Prettier configuration
├── jest.config.js    # Jest configuration
├── package.json      # Project dependencies
└── tsconfig.json     # TypeScript configuration
```

## Prerequisites

- Node.js >= 18.0.0
- SQL Server
- npm or yarn

## Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd store-management-system
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create environment file:
   ```bash
   cp .env.example .env
   ```
   Edit `.env` with your configuration

4. Run database migrations:
   ```bash
   npm run migrate
   ```

5. Start the development server:
   ```bash
   npm run dev
   ```

## Available Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint errors
- `npm run format` - Format code with Prettier
- `npm run migrate` - Run database migrations
- `npm run migrate:undo` - Undo last migration
- `npm run migrate:undo:all` - Undo all migrations

## API Documentation

API documentation is available at `/api-docs` when the server is running.

## Security

- Helmet.js for security headers
- CORS configuration
- Rate limiting
- Session security
- File upload restrictions
- Input validation
- SQL injection prevention (Sequelize)

## Error Handling

The application includes a centralized error handling system that:
- Catches and processes all errors
- Provides appropriate error responses
- Logs errors with context
- Handles different types of errors (API, Database, Validation)

## Logging

Logging is implemented using Winston with:
- Different log levels
- File rotation
- Separate error logs
- Request logging
- Development console output

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the ISC License. 