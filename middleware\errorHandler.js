// src/middleware/errorHandler.js

function errorHandler(err, req, res, next) {
    console.error('❌ Error:', err);
  
    // تحديد نوع الخطأ
    let statusCode = err.statusCode || 500;
    let errorMessage = err.message || 'Internal Server Error';
    let errorDetails = err.details;
    
    // معالجة أخطاء Sequelize
    if (err.name === 'SequelizeValidationError') {
        statusCode = 400;
        errorMessage = 'Validation Error';
        errorDetails = err.errors.map(e => e.message).join(', ');
    } else if (err.name === 'SequelizeUniqueConstraintError') {
        statusCode = 409;
        errorMessage = 'Duplicate Entry';
        errorDetails = 'A record with this information already exists';
    } else if (err.name === 'SequelizeDatabaseError') {
        errorDetails = err.original ? err.original.message : err.message;
    }
    
    // تحديد نوع الاستجابة بناءً على نوع الطلب
    if (req.xhr || req.path.startsWith('/api/')) {
        // استجابة API
        return res.status(statusCode).json({
            success: false,
            message: errorMessage,
            details: errorDetails,
            stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
        });
    } else {
        // استجابة صفحة
        return res.status(statusCode).render('error', {
            error: {
                status: statusCode,
                message: errorMessage,
                details: errorDetails,
                stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
                sql: err.sql
            }
        });
    }
}
  
module.exports = errorHandler;
  
