'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // إضافة عمود الصلاحيات فقط لأن الأعمدة الأخرى موجودة بالفعل
    await queryInterface.addColumn('Customers', 'permissions', {
      type: Sequelize.TEXT,
      allowNull: true,
      defaultValue: null,
      comment: 'صلاحيات العميل (JSON format)'
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('Customers', 'permissions');
  }
};
