<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="h4 mb-0">
                        <i class="fas fa-bell text-primary"></i>
                        تفاصيل الإشعار
                    </h2>
                    <p class="text-muted mb-0">معرف الإشعار: #<%= notification.id %></p>
                </div>
                <div class="btn-group">
                    <a href="/notifications" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للقائمة
                    </a>
                    <% if (!notification.readAt) { %>
                        <button type="button" class="btn btn-success" onclick="markAsRead(<%= notification.id %>)">
                            <i class="fas fa-check"></i> وضع كمقروء
                        </button>
                    <% } %>
                    <button type="button" class="btn btn-danger" onclick="deleteNotification(<%= notification.id %>)">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>

            <!-- Notification Card -->
            <div class="card notification-detail <%= !notification.readAt ? 'unread' : '' %>">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-<%= getTypeColor(notification.type) %> me-2">
                                <i class="fas fa-<%= getTypeIcon(notification.type) %>"></i>
                                <%= getTypeLabel(notification.type) %>
                            </span>
                            <span class="badge bg-<%= getPriorityColor(notification.priority) %>">
                                <%= getPriorityLabel(notification.priority) %>
                            </span>
                            <% if (!notification.readAt) { %>
                                <span class="badge bg-warning ms-2">
                                    <i class="fas fa-eye-slash"></i> غير مقروء
                                </span>
                            <% } else { %>
                                <span class="badge bg-success ms-2">
                                    <i class="fas fa-check"></i> مقروء
                                </span>
                            <% } %>
                        </div>
                        <small class="text-muted">
                            <i class="fas fa-clock"></i>
                            <%= new Date(notification.createdAt).toLocaleString('ar-SA') %>
                        </small>
                    </div>
                </div>
                
                <div class="card-body">
                    <h4 class="card-title mb-3"><%= notification.title %></h4>
                    <div class="notification-message">
                        <p class="card-text"><%= notification.message %></p>
                    </div>

                    <!-- Metadata -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h6 class="text-primary">
                                <i class="fas fa-info-circle"></i> معلومات الإرسال
                            </h6>
                            <div class="notification-meta">
                                <div class="meta-item">
                                    <strong>من:</strong>
                                    <% if (notification.admin) { %>
                                        <span class="badge bg-primary">
                                            <i class="fas fa-user-shield"></i>
                                            <%= notification.admin.name %>
                                        </span>
                                    <% } else { %>
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-cog"></i>
                                            النظام
                                        </span>
                                    <% } %>
                                </div>
                                
                                <div class="meta-item">
                                    <strong>إلى:</strong>
                                    <% if (notification.customer) { %>
                                        <span class="badge bg-success">
                                            <i class="fas fa-user"></i>
                                            <%= notification.customer.name %>
                                        </span>
                                    <% } else if (notification.admin) { %>
                                        <span class="badge bg-primary">
                                            <i class="fas fa-user-shield"></i>
                                            المشرف
                                        </span>
                                    <% } else { %>
                                        <span class="badge bg-warning">
                                            <i class="fas fa-users"></i>
                                            جميع المستخدمين
                                        </span>
                                    <% } %>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="text-primary">
                                <i class="fas fa-calendar-alt"></i> التوقيتات
                            </h6>
                            <div class="notification-meta">
                                <div class="meta-item">
                                    <strong>تاريخ الإنشاء:</strong>
                                    <span class="text-muted">
                                        <%= new Date(notification.createdAt).toLocaleString('ar-SA') %>
                                    </span>
                                </div>
                                
                                <% if (notification.readAt) { %>
                                    <div class="meta-item">
                                        <strong>تاريخ القراءة:</strong>
                                        <span class="text-success">
                                            <%= new Date(notification.readAt).toLocaleString('ar-SA') %>
                                        </span>
                                    </div>
                                <% } %>
                                
                                <% if (notification.expiresAt) { %>
                                    <div class="meta-item">
                                        <strong>تاريخ انتهاء الصلاحية:</strong>
                                        <span class="<%= new Date(notification.expiresAt) > new Date() ? 'text-warning' : 'text-danger' %>">
                                            <%= new Date(notification.expiresAt).toLocaleString('ar-SA') %>
                                            <% if (new Date(notification.expiresAt) <= new Date()) { %>
                                                <i class="fas fa-exclamation-triangle"></i> منتهي الصلاحية
                                            <% } %>
                                        </span>
                                    </div>
                                <% } %>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Data -->
                    <% if (notification.data) { %>
                        <div class="mt-4">
                            <h6 class="text-primary">
                                <i class="fas fa-database"></i> بيانات إضافية
                            </h6>
                            <div class="bg-light p-3 rounded">
                                <pre class="mb-0"><%= JSON.stringify(notification.data, null, 2) %></pre>
                            </div>
                        </div>
                    <% } %>
                </div>

                <!-- Action Button -->
                <% if (notification.actionUrl && notification.actionText) { %>
                    <div class="card-footer">
                        <a href="<%= notification.actionUrl %>" class="btn btn-primary" target="_blank">
                            <i class="fas fa-external-link-alt"></i>
                            <%= notification.actionText %>
                        </a>
                    </div>
                <% } %>
            </div>

            <!-- Related Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-tools"></i> إجراءات متاحة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <% if (!notification.readAt) { %>
                                <button type="button" class="btn btn-success w-100 mb-2" onclick="markAsRead(<%= notification.id %>)">
                                    <i class="fas fa-check"></i> وضع كمقروء
                                </button>
                            <% } else { %>
                                <button type="button" class="btn btn-outline-secondary w-100 mb-2" onclick="markAsUnread(<%= notification.id %>)">
                                    <i class="fas fa-eye-slash"></i> وضع كغير مقروء
                                </button>
                            <% } %>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-danger w-100 mb-2" onclick="deleteNotification(<%= notification.id %>)">
                                <i class="fas fa-trash"></i> حذف الإشعار
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Helper Functions -->
<%
function getTypeColor(type) {
    const colors = {
        'info': 'info',
        'success': 'success',
        'warning': 'warning',
        'error': 'danger',
        'order': 'primary',
        'promotion': 'success',
        'system': 'secondary'
    };
    return colors[type] || 'secondary';
}

function getTypeIcon(type) {
    const icons = {
        'info': 'info-circle',
        'success': 'check-circle',
        'warning': 'exclamation-triangle',
        'error': 'times-circle',
        'order': 'shopping-cart',
        'promotion': 'tag',
        'system': 'cog'
    };
    return icons[type] || 'bell';
}

function getTypeLabel(type) {
    const labels = {
        'info': 'معلومات',
        'success': 'نجاح',
        'warning': 'تحذير',
        'error': 'خطأ',
        'order': 'طلب',
        'promotion': 'عرض',
        'system': 'نظام'
    };
    return labels[type] || type;
}

function getPriorityColor(priority) {
    const colors = {
        'low': 'secondary',
        'normal': 'info',
        'high': 'warning',
        'urgent': 'danger'
    };
    return colors[priority] || 'secondary';
}

function getPriorityLabel(priority) {
    const labels = {
        'low': 'منخفضة',
        'normal': 'عادية',
        'high': 'عالية',
        'urgent': 'عاجلة'
    };
    return labels[priority] || priority;
}
%>

<script>
// Mark notification as read
function markAsRead(notificationId) {
    fetch(`/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

// Delete notification
function deleteNotification(notificationId) {
    if (!confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
        return;
    }

    fetch(`/notifications/${notificationId}/delete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = '/notifications';
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}
</script>
