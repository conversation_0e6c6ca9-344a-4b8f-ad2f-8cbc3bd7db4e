
const logger = require('../utils/logger');
const NotificationService = require('../services/NotificationService');
const { Order, Customer, OrderDetail, Product, Delivery, DeliveryPerson, Admin } = require('../models');
const { Op } = require('sequelize');
class NotificationsFunction {
  
    async sendNewOrderNotifications(order) {
        try {
            // جلب تفاصيل الطلب مع العلاقات
            const orderWithDetails = await Order.findByPk(order.id, {
                include: [
                    {
                        model: Customer,
                        as: 'customer',
                        attributes: ['id', 'name', 'phoneNumber']
                    },
                    {
                        model: OrderDetail,
                        as: 'orderDetails',
                        include: [{
                            model: Product,
                            as: 'product'
                        }]
                    }
                ]
            });

            if (!orderWithDetails) {
                logger.error('Order not found for notifications:', order.id);
                return;
            }

            // إرسال إشعار تأكيد للعميل
            await NotificationService.notifyCustomer(orderWithDetails.customer.id, {
                title: 'تم إنشاء طلبك بنجاح',
                message: `تم إنشاء طلبك رقم #${orderWithDetails.id} بنجاح بإجمالي ${orderWithDetails.totalPrice} ليرة. سيتم معالجته من قبل الشركة قريباً.`,
                type: 'success',
                priority: 'normal',
                customerId: orderWithDetails.customer.id,
                actionUrl: `/customers/orders/${orderWithDetails.id}`,
                actionText: 'عرض الطلب',
                data: {
                    orderId: orderWithDetails.id,
                    totalPrice: orderWithDetails.totalPrice,
                    type: 'order_created'
                }
            });

            // إرسال إشعار للمدراء
            await NotificationService.notifyAllAdmins({
                title: 'طلب جديد في النظام',
                message: `تم إنشاء طلب جديد رقم #${orderWithDetails.id} من العميل ${orderWithDetails.customer.name}\nإجمالي المبلغ: ${orderWithDetails.totalPrice} ليرة\n`,
                type: 'order',
                priority: 'normal',
                customerId: orderWithDetails.customer.id,
                actionUrl: `/admin/orders/${orderWithDetails.id}`,
                actionText: 'عرض تفاصيل الطلب',
                data: {
                    orderId: orderWithDetails.id,
                    customerId: orderWithDetails.customer.id,
                    customerName: orderWithDetails.customer.name,
                    totalPrice: orderWithDetails.totalPrice,
                    type: 'new_order_admin'
                }
            });

            logger.info(`Order notifications sent for order ${orderWithDetails.id} to customer ${orderWithDetails.customer.id}`);

            // إرسال إشعارات Firebase للعميل والمديرين
            try {
                const FirebaseMessagingService = require('../services/FirebaseMessagingService');

                // إشعار للعميل
                const customerNotification = {
                    title: 'تم إنشاء طلبك بنجاح',
                    body: `تم إنشاء طلبك رقم #${orderWithDetails.id} بإجمالي ${orderWithDetails.totalPrice} ليرة`
                };

                const customerData = {
                    orderId: orderWithDetails.id.toString(),
                    totalPrice: orderWithDetails.totalPrice.toString(),
                    clickAction: `/customers/orders/${orderWithDetails.id}`,
                    type: 'order_created'
                };

                await FirebaseMessagingService.sendToCustomer(
                    orderWithDetails.customer.id,
                    customerNotification,
                    customerData
                );

                // إشعار للمديرين
                const adminNotification = {
                    title: 'طلب جديد',
                    body: `تم تسجيل طلب جديد من ${orderWithDetails.customer.name} برقم #${order.id}`
                };

                const adminData = {
                    orderId: order.id.toString(),
                    customerName: orderWithDetails.customer.name,
                    clickAction: `/admin/orders/${order.id}`,
                    type: 'new_order'
                };

                await FirebaseMessagingService.sendToAllAdmins(adminNotification, adminData);

            } catch (notificationError) {
                console.error('❌ Failed to send Firebase notifications:', notificationError.message);
                // لا نوقف العملية إذا فشل الإشعار
            }
                    
        } catch (error) {
            logger.error('Error in sendNewOrderNotifications:', error);
            throw error;
        }
    }

    async completedOrderNotifications(order) {
        try {
            // جلب تفاصيل الطلب مع العلاقات
            const orderWithDetails = await Order.findByPk(order.id, {
                include: [
                    {
                        model: Customer,
                        as: 'customer',
                        attributes: ['id', 'name', 'phoneNumber']
                    },
                    {
                        model: OrderDetail,
                        as: 'orderDetails',
                        include: [{
                            model: Product,
                            as: 'product'
                        }]
                    },
                    {
                        model: Delivery,
                        as: 'delivery',
                        include: [
                            {
                                model: DeliveryPerson,
                                as: 'courier',
                                attributes: ['id', 'name']
                            }
                        ]
                    }
                ]
            });


            if (!orderWithDetails) {
                logger.error('Order not found for notifications:', order.id);
                return;
            }

            // إرسال إشعار تأكيد للعميل
            await NotificationService.notifyCustomer(orderWithDetails.customer.id, {
                title: 'تم توصيل طلبك بنجاح',
                message: `تم توصيل طلبك رقم #${orderWithDetails.id} بنجاح بإجمالي ${orderWithDetails.totalPrice} ليرة.`,
                type: 'success',
                priority: 'normal',
                customerId: orderWithDetails.customer.id,
                actionUrl: `/customers/orders/${orderWithDetails.id}`,
                actionText: 'عرض الطلب',
                data: {
                    orderId: orderWithDetails.id,
                    totalPrice: orderWithDetails.totalPrice,
                    type: 'order_created'
                }
            });
             // إرسال إشعار للسائق
            await NotificationService.notifyDriver(orderWithDetails.delivery.courier.id, {
                title: 'تم توصيل الطلب',
                message: `تم توصيل الطلب رقم #${orderWithDetails.id}  للعميل ${orderWithDetails.customer.name}\nإجمالي المبلغ: ${orderWithDetails.totalPrice} ليرة\n`,
                type: 'success',
                priority: 'normal',
                deliveryPersonId: orderWithDetails.delivery.courier.id,
                actionUrl: `/customers/delivery/${orderWithDetails.delivery.id}`,
                actionText: 'عرض تفاصيل الطلب',
                data: {
                    orderId: orderWithDetails.id,
                    customerId: orderWithDetails.customer.id,
                    customerName: orderWithDetails.customer.name,
                    deliveryId: orderWithDetails.delivery.id,
                    totalPrice: orderWithDetails.totalPrice,
                    type: 'deliver-approved'
                }
            });

            // إرسال إشعار للمدراء
            await NotificationService.notifyAllAdmins({
                title: 'استلام طلب',
                message: `تم استلام الطلب رقم #${orderWithDetails.id} من قبل العميل ${orderWithDetails.customer.name}\nإجمالي المبلغ: ${orderWithDetails.totalPrice} ليرة\n`,
                type: 'order',
                priority: 'normal',
                customerId: orderWithDetails.customer.id,
                actionUrl: `/admin/orders/${orderWithDetails.id}`,
                actionText: 'عرض تفاصيل الطلب',
                data: {
                    orderId: orderWithDetails.id,
                    customerId: orderWithDetails.customer.id,
                    customerName: orderWithDetails.customer.name,
                    totalPrice: orderWithDetails.totalPrice,
                    type: 'new_order_admin'
                }
            });

            logger.info(`Order notifications sent for order ${orderWithDetails.id} to customer ${orderWithDetails.customer.id}`);

            // إرسال إشعارات Firebase للعميل والمديرين
            try {
                const FirebaseMessagingService = require('../services/FirebaseMessagingService');

                // إشعار للعميل
                const customerNotification = {
                    title: 'تم توصيل طلبك بنجاح',
                    body: `تم توصيل طلبك رقم #${orderWithDetails.id} بنجاح. شكراً لاختيارك خدماتنا!`
                };

                const customerData = {
                    orderId: orderWithDetails.id.toString(),
                    totalPrice: orderWithDetails.totalPrice.toString(),
                    clickAction: `/customers/orders/${orderWithDetails.id}`,
                    type: 'order_completed'
                };

                await FirebaseMessagingService.sendToCustomer(
                    orderWithDetails.customer.id,
                    customerNotification,
                    customerData
                );

                // إشعار للسائق
                const driverNotification = {
                    title: 'توصيل طلب جديد',
                    body: `تم اسناد توصيل جديد للطلب رقم #${orderWithDetails.id} بإجمالي ${orderWithDetails.totalPrice} ليرة \n`
                };

                const driverData = {
                    deliveryId: orderWithDetails.delivery.id.toString(),
                    orderId: orderWithDetails.id.toString(),
                    driverId: orderWithDetails.delivery.courier.id?.toString() || '',
                    driverName: orderWithDetails.delivery.courier?.name || 'غير محدد',
                    customerName: orderWithDetails?.customer?.name || 'غير محدد',
                    clickAction: `/customers/delivery/${orderWithDetails.delivery.id}`,
                    type: 'new_delivery'
                };

                await FirebaseMessagingService.sendToDriver(
                    orderWithDetails.delivery.courier.id,
                    driverNotification,
                    driverData
                );

                // إشعار للمديرين
                const adminNotification = {
                    title: 'الطلب مكتمل',
                    body: `تم تسليم الطلب رقم #${order.id} إلى العميل ${orderWithDetails.customer.name}`
                };

                const adminData = {
                    orderId: order.id.toString(),
                    customerName: orderWithDetails.customer.name,
                    clickAction: `/admin/orders/${order.id}`,
                    type: 'order_completed'
                };

                await FirebaseMessagingService.sendToAllAdmins(adminNotification, adminData);

            } catch (notificationError) {
                console.error('❌ Failed to send Firebase notifications:', notificationError.message);
                // لا نوقف العملية إذا فشل الإشعار
            }
                    
        } catch (error) {
            logger.error('Error in sendNewOrderNotifications:', error);
            throw error;
        }
    }

    async canceledOrderNotifications(order) {
        try {
            // جلب تفاصيل الطلب مع العلاقات
            const orderWithDetails = await Order.findByPk(order.id, {
                include: [
                    {
                        model: Customer,
                        as: 'customer',
                        attributes: ['id', 'name', 'phoneNumber']
                    },
                    {
                        model: OrderDetail,
                        as: 'orderDetails',
                        include: [{
                            model: Product,
                            as: 'product'
                        }]
                    }
                ]
            });

            if (!orderWithDetails) {
                logger.error('Order not found for notifications:', order.id);
                return;
            }

            // إرسال إشعار تأكيد للعميل
            await NotificationService.notifyCustomer(orderWithDetails.customer.id, {
                title: 'تم إلغاء طلبك بنجاح',
                message: `تم إلغاء طلبك رقم #${orderWithDetails.id} بنجاح بإجمالي ${orderWithDetails.totalPrice} ليرة.`,
                type: 'success',
                priority: 'normal',
                customerId: orderWithDetails.customer.id,
                actionUrl: `/customers/orders/${orderWithDetails.id}`,
                actionText: 'عرض الطلب',
                data: {
                    orderId: orderWithDetails.id,
                    totalPrice: orderWithDetails.totalPrice,
                    type: 'order_created'
                }
            });

            // إرسال إشعار للمدراء
            await NotificationService.notifyAllAdmins({
                title: 'إلغاء طلب',
                message: `تم إلغاء الطلب رقم #${orderWithDetails.id} من قبل العميل ${orderWithDetails.customer.name}\nإجمالي المبلغ: ${orderWithDetails.totalPrice} ليرة\n`,
                type: 'order',
                priority: 'normal',
                customerId: orderWithDetails.customer.id,
                actionUrl: `/admin/orders/${orderWithDetails.id}`,
                actionText: 'عرض تفاصيل الطلب',
                data: {
                    orderId: orderWithDetails.id,
                    customerId: orderWithDetails.customer.id,
                    customerName: orderWithDetails.customer.name,
                    totalPrice: orderWithDetails.totalPrice,
                    type: 'new_order_admin'
                }
            });

            logger.info(`Order notifications sent for order ${orderWithDetails.id} to customer ${orderWithDetails.customer.id}`);

            // إرسال إشعارات Firebase للعميل والمديرين
            try {
                const FirebaseMessagingService = require('../services/FirebaseMessagingService');

                // إشعار للعميل
                const customerNotification = {
                    title: 'تم إلغاء طلبك',
                    body: `تم إلغاء طلبك رقم #${orderWithDetails.id}. نعتذر عن أي إزعاج.`
                };

                const customerData = {
                    orderId: orderWithDetails.id.toString(),
                    totalPrice: orderWithDetails.totalPrice.toString(),
                    clickAction: `/customers/orders/${orderWithDetails.id}`,
                    type: 'order_cancelled'
                };

                await FirebaseMessagingService.sendToCustomer(
                    orderWithDetails.customer.id,
                    customerNotification,
                    customerData
                );

                // إشعار للمديرين
                const adminNotification = {
                    title: 'الطلب ملغي',
                    body: `تم إلغاء الطلب رقم #${order.id} للعميل ${orderWithDetails.customer.name}`
                };

                const adminData = {
                    orderId: order.id.toString(),
                    customerName: orderWithDetails.customer.name,
                    clickAction: `/admin/orders/${order.id}`,
                    type: 'order_cancelled'
                };

                await FirebaseMessagingService.sendToAllAdmins(adminNotification, adminData);

            } catch (notificationError) {
                console.error('❌ Failed to send Firebase notifications:', notificationError.message);
                // لا نوقف العملية إذا فشل الإشعار
            }
                    
        } catch (error) {
            logger.error('Error in sendNewOrderNotifications:', error);
            throw error;
        }
    } 

    async sendNewDeliveryNotifications(delivery) {
      try {
          // جلب تفاصيل التوصيل مع العلاقات
          const deliveryWithDetails = await Delivery.findByPk(delivery.id, {
              include: [
                  {
                      model: Order,
                      as: 'order',
                      include: [
                          { model: Customer, as: 'customer' },
                          { 
                              model: OrderDetail, 
                              as: 'orderDetails',
                              include: [{ model: Product, as: 'product' }]
                          },
                      ]
                  },
                  {
                      model: DeliveryPerson,
                      as: 'courier'
                  }
              ]
          });

          if (!deliveryWithDetails) {
              logger.error('Delivery not found for notifications:', delivery.id);
              return;
          }

          const orderId = deliveryWithDetails.order.id;
          const deliveryPersonId = deliveryWithDetails.courier?.id;

          // إرسال إشعار تأكيد للعميل
          await NotificationService.notifyCustomer(deliveryWithDetails.order.customer.id, {
              title: 'تم قبول طلبك',
              message: `تم قبول طلبك رقم #${orderId} بنجاح بإجمالي ${deliveryWithDetails.order.totalPrice} ليرة.\nسيتم توصيله من قبل ${deliveryWithDetails.courier?.name || 'سائق'}`,
              type: 'success',
              priority: 'normal',
              customerId: deliveryWithDetails.order.customer.id,
              actionUrl: `/customers/orders/${orderId}`,
              actionText: 'عرض الطلب',
              data: {
                  orderId: orderId,
                  totalPrice: deliveryWithDetails.order.totalPrice,
                  type: 'order_approved'
              }
          });

           // إرسال إشعار للسائق
          await NotificationService.notifyDriver(deliveryWithDetails.courier.id, {
              title: 'توصيل طلب جديد',
              message: `تم اسناد توصيل جديد للطلب رقم #${deliveryWithDetails.order.id} من العميل ${deliveryWithDetails.order.customer.name}\nإجمالي المبلغ: ${deliveryWithDetails.order.totalPrice} ليرة\n`,
              type: 'task',
              priority: 'normal',
              deliveryPersonId: deliveryWithDetails.courier.id,
              actionUrl: `/customers/delivery/${deliveryWithDetails.id}`,
              actionText: 'عرض تفاصيل الطلب',
              data: {
                  orderId: deliveryWithDetails.order.id,
                  customerId: deliveryWithDetails.order.customer.id,
                  customerName: deliveryWithDetails.order.customer.name,
                  deliveryId: deliveryWithDetails.id,
                  totalPrice: deliveryWithDetails.order.totalPrice,
                  type: 'new_delivery'
              }
          });

          // إرسال إشعار للمدراء
          await NotificationService.notifyAllAdmins({
              title: 'توصيل طلب',
              message: `تم اسناد توصيل جديد للطلب رقم #${deliveryWithDetails.order.id} من العميل ${deliveryWithDetails.order.customer.name}\nإجمالي المبلغ: ${deliveryWithDetails.order.totalPrice} ليرة\n`,
              type: 'info',
              priority: 'normal',
              customerId: deliveryWithDetails.order.customer.id,
              actionUrl: `/admin/orders/${deliveryWithDetails.order.id}`,
              actionText: 'عرض تفاصيل الطلب',
              data: {
                  orderId: deliveryWithDetails.order.id,
                  customerId: deliveryWithDetails.order.customer.id,
                  customerName: deliveryWithDetails.order.customer.name,
                  totalPrice: deliveryWithDetails.order.totalPrice,
                  type: 'new_order_admin'
              }
          });

          logger.info(`Order notifications sent for order ${orderId} to customer ${deliveryWithDetails.order.customer.id}`);

            // إرسال إشعارات Firebase للعميل والمديرين
          try {
              const FirebaseMessagingService = require('../services/FirebaseMessagingService');

              // إشعار للعميل
              const customerNotification = {
                  title: 'تم قبول طلبك ',
                  body: `تم قبول طلبك رقم #${deliveryWithDetails.order.id} بإجمالي ${deliveryWithDetails.order.totalPrice} ليرة \nسيتم توصيله من قبل ${deliveryWithDetails.courier?.name || 'سائق'}`
              };

              const customerData = {
                  deliveryId: delivery.id.toString(),
                  orderId: orderId.toString(),
                  driverId: deliveryPersonId?.toString() || '',
                  driverName: deliveryWithDetails.courier?.name || 'غير محدد',
                  customerName: deliveryWithDetails.order?.customer?.name || 'غير محدد',
                  clickAction: `/admin/deliveries/${delivery.id}`,
                  type: 'customer_delivery'
              };

              await FirebaseMessagingService.sendToCustomer(
                  deliveryWithDetails.order.customer.id,
                  customerNotification,
                  customerData
              );

               // إشعار للسائق
              const driverNotification = {
                  title: 'توصيل طلب جديد',
                  body: `تم اسناد توصيل جديد للطلب رقم #${deliveryWithDetails.order.id} بإجمالي ${deliveryWithDetails.order.totalPrice} ليرة \n`
              };

              const driverData = {
                  deliveryId: delivery.id.toString(),
                  orderId: orderId.toString(),
                  driverId: deliveryPersonId?.toString() || '',
                  driverName: deliveryWithDetails.courier?.name || 'غير محدد',
                  customerName: deliveryWithDetails.order?.customer?.name || 'غير محدد',
                  clickAction: `/customers/delivery/${delivery.id}`,
                  type: 'new_delivery'
              };

              await FirebaseMessagingService.sendToDriver(
                  deliveryWithDetails.courier.id,
                  driverNotification,
                  driverData
              );

              // إشعار للمديرين
              const adminNotification = {
                  title: 'توصيل جديد',
                  body: `تم إنشاء توصيل جديد للطلب #${orderId} - السائق: ${deliveryWithDetails.courier?.name || 'غير محدد'}`
              };

              const adminData = {
                  deliveryId: delivery.id.toString(),
                  orderId: orderId.toString(),
                  driverId: deliveryPersonId?.toString() || '',
                  driverName: deliveryWithDetails.courier?.name || 'غير محدد',
                  customerName: deliveryWithDetails.order?.customer?.name || 'غير محدد',
                  clickAction: `/admin/deliveries/${delivery.id}`,
                  type: 'new_delivery'
              };

              await FirebaseMessagingService.sendToAllAdmins(adminNotification, adminData);

          } catch (notificationError) {
              console.error('❌ Failed to send Firebase notifications:', notificationError.message);
              // لا نوقف العملية إذا فشل الإشعار
          }

      } catch (error) {
          logger.error('Error in sendNewDeliveryNotifications:', error);
          throw error;
      }
    }

    async sendApprovedOrderNotifications(order) {
        try {
            // جلب تفاصيل الطلب مع العلاقات
            const orderWithDetails = await Order.findByPk(order.id, {
                include: [
                    {
                        model: Customer,
                        as: 'customer',
                        attributes: ['id', 'name', 'phoneNumber']
                    },
                    {
                        model: OrderDetail,
                        as: 'orderDetails',
                        include: [{
                            model: Product,
                            as: 'product'
                        }]
                    }
                ]
            });

            if (!orderWithDetails) {
                logger.error('Order not found for notifications:', order.id);
                return;
            }

             // إرسال إشعار للعميل
            await NotificationService.notifyCustomer(orderWithDetails.customer.id, {
                    title: 'تم قبول طلبك',
                    message: `تم قبول طلبك رقم #${order.id} بنجاح بإجمالي ${orderWithDetails.totalPrice} ليرة.`,
                    type: 'success',
                    priority: 'normal',
                    customerId: orderWithDetails.customer.id,
                    actionUrl: `/customers/orders/${orderWithDetails.id}`,
                    actionText: 'عرض الطلب',
                    data: {
                        orderId: orderWithDetails.id,
                        totalPrice: orderWithDetails.totalPrice,
                        type: 'order_approved'
                    }
            });
        

            // إرسال إشعار للمدراء
            await NotificationService.notifyAllAdmins({
                title: 'طلب مرفوض',
                message: `تم قبول طلب  رقم #${orderWithDetails.id} من العميل ${orderWithDetails.customer.name}\ بنجاح بإجمالي ${orderWithDetails.totalPrice} ليرة.\n`,
                type: 'order',
                priority: 'normal',
                customerId: orderWithDetails.customer.id,
                actionUrl: `/admin/orders/${orderWithDetails.id}`,
                actionText: 'عرض تفاصيل الطلب',
                data: {
                    orderId: orderWithDetails.id,
                    customerId: orderWithDetails.customer.id,
                    customerName: orderWithDetails.customer.name,
                    totalPrice: orderWithDetails.totalPrice,
                    type: 'order_approved'
                }
            });

            logger.info(`Order notifications sent for order ${orderWithDetails.id} to customer ${orderWithDetails.customer.id}`);

            // إرسال إشعارات Firebase للعميل والمديرين
            try {
                const FirebaseMessagingService = require('../services/FirebaseMessagingService');

                // إشعار للعميل
                const customerNotification = {
                    title: 'تم قبول طلبك ',
                    body: `تم قبول طلبك رقم #${orderWithDetails.id} بإجمالي ${orderWithDetails.totalPrice} ليرة `
                };

                const customerData = {
                    orderId: orderWithDetails.id.toString(),
                    totalPrice: orderWithDetails.totalPrice.toString(),
                    clickAction: `/customers/orders/${orderWithDetails.id}`,
                    type: 'order_approved'
                };

                await FirebaseMessagingService.sendToCustomer(
                    orderWithDetails.customer.id,
                    customerNotification,
                    customerData
                );

                // إشعار للمديرين
                const adminNotification = {
                    title: 'طلب مقبول',
                    body: `تم قبول الطلب من ${orderWithDetails.customer.name} برقم #${orderWithDetails.id}\nإجمالي المبلغ: ${orderWithDetails.totalPrice} ليرة\n`
                };

                const adminData = {
                    orderId: orderWithDetails.id.toString(),
                    customerName: orderWithDetails.customer.name,
                    clickAction: `/admin/orders/${orderWithDetails.id}`,
                    type: 'order_approved'
                };

                await FirebaseMessagingService.sendToAllAdmins(adminNotification, adminData);

            } catch (notificationError) {
                console.error('❌ Failed to send Firebase notifications:', notificationError.message);
                // لا نوقف العملية إذا فشل الإشعار
            }
                    
        } catch (error) {
            logger.error('Error in sendNewOrderNotifications:', error);
            throw error;
        }
    }

    async sendrejectedOrderNotifications(order) {
        try {
            // جلب تفاصيل الطلب مع العلاقات
            const orderWithDetails = await Order.findByPk(order.id, {
                include: [
                    {
                        model: Customer,
                        as: 'customer',
                        attributes: ['id', 'name', 'phoneNumber']
                    },
                    {
                        model: OrderDetail,
                        as: 'orderDetails',
                        include: [{
                            model: Product,
                            as: 'product'
                        }]
                    }
                ]
            });

            if (!orderWithDetails) {
                logger.error('Order not found for notifications:', order.id);
                return;
            }

             // إرسال إشعار للعميل
            await NotificationService.notifyCustomer(order.customer.id, {
                title: 'تم رفض طلبك',
                message: `تم رفض طلبك رقم #${order.id}${orderWithDetails.notes ? ` - السبب: ${orderWithDetails.notes}` : ''}`,
                type: 'error',
                priority: 'high',
                actionUrl: `/customers/orders/${order.id}`,
                actionText: 'عرض التفاصيل',
                data: {
                    orderId: order.id,
                    type: 'order_rejected',
                    notes: orderWithDetails.notes
                }
            });
        

            // إرسال إشعار للمدراء
            await NotificationService.notifyAllAdmins({
                title: 'طلب مرفوض',
                message: `تم رفض طلب  رقم #${orderWithDetails.id} من العميل ${orderWithDetails.customer.name}\nسبب الرفض: ${orderWithDetails.notes}\n`,
                type: 'order',
                priority: 'normal',
                customerId: orderWithDetails.customer.id,
                actionUrl: `/admin/orders/${orderWithDetails.id}`,
                actionText: 'عرض تفاصيل الطلب',
                data: {
                    orderId: orderWithDetails.id,
                    customerId: orderWithDetails.customer.id,
                    customerName: orderWithDetails.customer.name,
                    totalPrice: orderWithDetails.totalPrice,
                    type: 'order_rejected'
                }
            });

            logger.info(`Order notifications sent for order ${orderWithDetails.id} to customer ${orderWithDetails.customer.id}`);

            // إرسال إشعارات Firebase للعميل والمديرين
            try {
                const FirebaseMessagingService = require('../services/FirebaseMessagingService');

                // إشعار للعميل
                const customerNotification = {
                    title: 'تم رفض طلبك ',
                    body: `تم رفض طلبك رقم #${orderWithDetails.id} بسبب ${orderWithDetails.notes} `
                };

                const customerData = {
                    orderId: orderWithDetails.id.toString(),
                    totalPrice: orderWithDetails.totalPrice.toString(),
                    clickAction: `/customers/orders/${orderWithDetails.id}`,
                    type: 'order_rejected'
                };

                await FirebaseMessagingService.sendToCustomer(
                    orderWithDetails.customer.id,
                    customerNotification,
                    customerData
                );

                // إشعار للمديرين
                const adminNotification = {
                    title: 'طلب مرفوض',
                    body: `تم رفض الطلب من ${orderWithDetails.customer.name} برقم #${order.id}\nسبب الرفض: ${orderWithDetails.notes}\n`
                };

                const adminData = {
                    orderId: order.id.toString(),
                    customerName: orderWithDetails.customer.name,
                    clickAction: `/admin/orders/${order.id}`,
                    type: 'order_rejected'
                };

                await FirebaseMessagingService.sendToAllAdmins(adminNotification, adminData);

            } catch (notificationError) {
                console.error('❌ Failed to send Firebase notifications:', notificationError.message);
                // لا نوقف العملية إذا فشل الإشعار
            }
                    
        } catch (error) {
            logger.error('Error in sendNewOrderNotifications:', error);
            throw error;
        }
    }

    async sendUpdatedOrderNotifications(order, status, notes) {
        const oldStatus = order.status;

        // إرسال إشعار للعميل عن طريق NotificationService
        const statusMessages = {
            'pending': 'تم تعليق طلبك',
            'approved': 'تمت الموافقة على طلبك وسيتم تحضيره قريباً',
            'rejected': 'تم رفض طلبك' + (notes ? ` - السبب: ${notes}` : ''),
            'preparing': 'جاري تحضير طلبك',
            'out_for_delivery': 'طلبك في الطريق إليك',
            'delivered': 'تم تسليم طلبك بنجاح',
            'cancelled': 'تم إلغاء طلبك' + (notes ? ` - السبب: ${notes}` : '')
        };

        const statusTypes = {
            'pending': 'info',
            'approved': 'success',
            'rejected': 'error',
            'preparing': 'info',
            'out_for_delivery': 'info',
            'delivered': 'success',
            'cancelled': 'warning'
        };

        const statusPriorities = {
            'pending': 'normal',
            'approved': 'high',
            'rejected': 'high',
            'preparing': 'normal',
            'out_for_delivery': 'high',
            'delivered': 'normal',
            'cancelled': 'high'
        };

        await NotificationService.notifyCustomer(order.customer.id, {
            title: 'تحديث حالة الطلب',
            message: `${statusMessages[status]} - طلب رقم #${order.id}`,
            type: statusTypes[status],
            priority: statusPriorities[status],
            actionUrl: `/customers/orders/${order.id}`,
            actionText: 'عرض تفاصيل الطلب',
            data: {
                orderId: order.id,
                status: status,
                type: 'order_status_update',
                notes: notes
            }
        });

        // إرسال إشعارات للعميل والمدراء عن تحديث حالة الطلب
        if (oldStatus !== status) {
            try {
                const FirebaseMessagingService = require('../services/FirebaseMessagingService');

                const statusTextAr = {
                    'pending': 'في الانتظار',
                    'processing': 'قيد المعالجة',
                    'rejected': 'مرفوض',
                    'out_for_delivery': 'في الطريق',
                    'completed': 'مكتمل',
                    'cancelled': 'ملغي'
                };

                // إشعار للعميل
                const customerNotification = {
                    title: 'تحديث حالة طلبك',
                    body: `تم تغيير حالة طلبك #${order.id} إلى: ${statusTextAr[status]}`
                };

                const customerData = {
                    orderId: order.id.toString(),
                    oldStatus: oldStatus,
                    newStatus: status,
                    clickAction: `/customers/orders/${order.id}`,
                    type: 'order_status_update'
                };

                await FirebaseMessagingService.sendToCustomer(
                    order.customer.id,
                    customerNotification,
                    customerData
                );

                // إشعار للمديرين
                const adminNotification = {
                    title: 'تحديث حالة طلب',
                    body: `تم تغيير حالة الطلب #${order.id} من ${statusTextAr[oldStatus]} إلى ${statusTextAr[status]}`
                };

                const adminData = {
                    orderId: order.id.toString(),
                    customerId: order.customer.id.toString(),
                    customerName: order.customer.name,
                    oldStatus: oldStatus,
                    newStatus: status,
                    clickAction: `/admin/orders/${order.id}`,
                    type: 'order_status_update_admin'
                };

                await FirebaseMessagingService.sendToAllAdmins(adminNotification, adminData);

            } catch (notificationError) {
                console.error('❌ Error sending order status notifications:', notificationError);
            }
        }
    }
}

module.exports = new NotificationsFunction();

