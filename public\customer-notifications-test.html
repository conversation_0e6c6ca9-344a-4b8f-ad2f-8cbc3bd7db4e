<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إشعارات العملاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        .status-granted { background-color: #28a745; }
        .status-denied { background-color: #dc3545; }
        .status-default { background-color: #ffc107; }
        .status-not-supported { background-color: #6c757d; }
        
        .log-container {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
        
        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }
        
        .log-success { background-color: #d4edda; color: #155724; }
        .log-error { background-color: #f8d7da; color: #721c24; }
        .log-info { background-color: #d1ecf1; color: #0c5460; }
        .log-warning { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">🔔 اختبار نظام إشعارات العملاء</h1>
            </div>
        </div>

        <!-- حالة النظام -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">حالة النظام</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <strong>حالة الإشعارات:</strong>
                            <span id="notification-status" class="badge bg-secondary">غير محدد</span>
                            <span id="status-indicator" class="status-indicator status-not-supported"></span>
                        </div>
                        <div class="mb-2">
                            <strong>Service Worker:</strong>
                            <span id="sw-status" class="badge bg-secondary">غير محدد</span>
                        </div>
                        <div class="mb-2">
                            <strong>Firebase:</strong>
                            <span id="firebase-status" class="badge bg-secondary">غير محدد</span>
                        </div>
                        <div class="mb-2">
                            <strong>التوكن:</strong>
                            <span id="token-status" class="badge bg-secondary">غير محدد</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">الإجراءات</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button id="init-btn" class="btn btn-primary" onclick="initializeSystem()">
                                تهيئة النظام
                            </button>
                            <button id="request-permission-btn" class="btn btn-success" onclick="requestPermission()" disabled>
                                طلب إذن الإشعارات
                            </button>
                            <button id="test-notification-btn" class="btn btn-info" onclick="testNotification()" disabled>
                                اختبار إشعار
                            </button>
                            <button id="clear-log-btn" class="btn btn-warning" onclick="clearLog()">
                                مسح السجل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات التوكن -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">معلومات التوكن</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <label for="auth-token" class="form-label">توكن المصادقة (JWT):</label>
                            <input type="text" id="auth-token" class="form-control" placeholder="أدخل توكن المصادقة هنا">
                            <div class="form-text">يجب إدخال توكن مصادقة صحيح لحفظ توكن Firebase</div>
                        </div>
                        <div class="mb-2">
                            <label for="firebase-token" class="form-label">توكن Firebase:</label>
                            <textarea id="firebase-token" class="form-control" rows="3" readonly placeholder="سيتم عرض التوكن هنا بعد التهيئة"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- سجل الأحداث -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">سجل الأحداث</h5>
                    </div>
                    <div class="card-body">
                        <div id="log-container" class="log-container">
                            <div class="log-entry log-info">
                                <strong>[INFO]</strong> مرحباً بك في نظام اختبار إشعارات العملاء
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-messaging-compat.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase Config -->
    <script>
        // إعدادات Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyBqJVJKvKKKvKKKvKKKvKKKvKKKvKKKvKK",
            authDomain: "your-project.firebaseapp.com",
            projectId: "your-project-id",
            storageBucket: "your-project.appspot.com",
            messagingSenderId: "123456789012",
            appId: "1:123456789012:web:abcdef123456789012345"
        };

        // تهيئة Firebase
        firebase.initializeApp(firebaseConfig);
    </script>
    
    <!-- Customer Firebase Notifications -->
    <script src="/js/customer-firebase.js"></script>
    
    <!-- Test Script -->
    <script>
        let isSystemInitialized = false;
        
        // تحديث حالة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateSystemStatus();
            checkServiceWorkerSupport();
            checkFirebaseSupport();
        });

        function log(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateSystemStatus() {
            const status = window.CustomerNotifications.getNotificationStatus();
            const statusElement = document.getElementById('notification-status');
            const indicatorElement = document.getElementById('status-indicator');
            
            switch(status) {
                case 'granted':
                    statusElement.textContent = 'مُفعل';
                    statusElement.className = 'badge bg-success';
                    indicatorElement.className = 'status-indicator status-granted';
                    break;
                case 'denied':
                    statusElement.textContent = 'مرفوض';
                    statusElement.className = 'badge bg-danger';
                    indicatorElement.className = 'status-indicator status-denied';
                    break;
                case 'default':
                    statusElement.textContent = 'في الانتظار';
                    statusElement.className = 'badge bg-warning';
                    indicatorElement.className = 'status-indicator status-default';
                    break;
                default:
                    statusElement.textContent = 'غير مدعوم';
                    statusElement.className = 'badge bg-secondary';
                    indicatorElement.className = 'status-indicator status-not-supported';
            }
        }

        function checkServiceWorkerSupport() {
            const swStatus = document.getElementById('sw-status');
            if ('serviceWorker' in navigator) {
                swStatus.textContent = 'مدعوم';
                swStatus.className = 'badge bg-success';
                log('Service Worker مدعوم في هذا المتصفح', 'success');
            } else {
                swStatus.textContent = 'غير مدعوم';
                swStatus.className = 'badge bg-danger';
                log('Service Worker غير مدعوم في هذا المتصفح', 'error');
            }
        }

        function checkFirebaseSupport() {
            const firebaseStatus = document.getElementById('firebase-status');
            if (typeof firebase !== 'undefined') {
                firebaseStatus.textContent = 'محمل';
                firebaseStatus.className = 'badge bg-success';
                log('Firebase SDK محمل بنجاح', 'success');
            } else {
                firebaseStatus.textContent = 'غير محمل';
                firebaseStatus.className = 'badge bg-danger';
                log('فشل في تحميل Firebase SDK', 'error');
            }
        }

        async function initializeSystem() {
            try {
                log('بدء تهيئة النظام...', 'info');
                
                const authToken = document.getElementById('auth-token').value.trim();
                if (!authToken) {
                    throw new Error('يجب إدخال توكن المصادقة أولاً');
                }
                
                await window.CustomerNotifications.initialize(authToken);
                
                isSystemInitialized = true;
                document.getElementById('request-permission-btn').disabled = false;
                document.getElementById('init-btn').disabled = true;
                
                log('تم تهيئة النظام بنجاح ✅', 'success');
                updateSystemStatus();
                
            } catch (error) {
                log(`فشل في تهيئة النظام: ${error.message}`, 'error');
            }
        }

        async function requestPermission() {
            try {
                log('طلب إذن الإشعارات...', 'info');
                
                const token = await window.CustomerNotifications.requestPermissionAndSaveToken();
                
                document.getElementById('firebase-token').value = token;
                document.getElementById('test-notification-btn').disabled = false;
                document.getElementById('token-status').textContent = 'محفوظ';
                document.getElementById('token-status').className = 'badge bg-success';
                
                log('تم الحصول على إذن الإشعارات وحفظ التوكن ✅', 'success');
                updateSystemStatus();
                
            } catch (error) {
                log(`فشل في طلب الإذن: ${error.message}`, 'error');
                updateSystemStatus();
            }
        }

        function testNotification() {
            try {
                log('اختبار إشعار محلي...', 'info');
                
                window.CustomerNotifications.showNotification(
                    {
                        title: 'إشعار تجريبي',
                        body: 'هذا إشعار تجريبي لاختبار النظام'
                    },
                    {
                        type: 'test',
                        clickAction: '/customers/orders'
                    }
                );
                
                log('تم إرسال الإشعار التجريبي ✅', 'success');
                
            } catch (error) {
                log(`فشل في إرسال الإشعار التجريبي: ${error.message}`, 'error');
            }
        }

        function clearLog() {
            const logContainer = document.getElementById('log-container');
            logContainer.innerHTML = '<div class="log-entry log-info"><strong>[INFO]</strong> تم مسح السجل</div>';
        }
    </script>
</body>
</html>
