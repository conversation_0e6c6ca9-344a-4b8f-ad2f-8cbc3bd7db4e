/* Notification System Styles - Professional CSS Organization */

/* Notification Status Indicators */
.notification-status-indicator {
    position: static;
    top: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    background-color: #dc3545; /* Red by default (disabled) */
    transition: background-color 0.3s ease;
}

.notification-status-indicator.enabled {
    background-color: #4bf773; /* Green when enabled */
}

.notification-status-indicator.pending {
    background-color: #ffc107; /* Yellow when pending */
    animation: pulse 1.5s infinite;
}

.notification-status-dot {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #dc3545;
    transition: background-color 0.3s ease;
}

.notification-status-dot.enabled {
    background-color: #28a745;
}

.notification-status-dot.pending {
    background-color: #ffc107;
    animation: pulse 1.5s infinite;
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    min-width: 18px;
    text-align: center;
    animation: bounce 0.5s ease-in-out;
}

/* Notification Bell Button */
#notificationDropdown {
    position: relative;
    font-size: 1.2rem;
}

#notificationDropdown .fa-bell {
    color: #fff;
    transition: color 0.3s ease;
}

#notificationDropdown:hover .fa-bell {
    color: #22c55e;
}

/* Notification Status Dot for Bell */
#notificationStatusDot {
    position: absolute;
    top: 6px;
    right: 8px;
    width: 10px;
    height: 10px;
    background: #dc3545;
    border-radius: 50%;
    border: 2px solid white;
    display: none;
    z-index: 10;
}

/* Notification Count Badge */
#notificationBadge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #d10d17;
    color: white;
    font-size: 0.65rem;
    font-weight: 700;
    min-width: 18px;
    height: 18px;
    line-height: 18px;
    border-radius: 9px;
    text-align: center;
    padding: 0 5px;
    z-index: 10;
}

/* Dropdown Menu Styles */
.dropdown-menu {
    width: 360px;
    max-height: 450px;
    overflow-y: auto;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
    padding: 0;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    background: #f9f9f9;
}

.dropdown-header {
    background: #22c55e;
    color: white;
    font-weight: 600;
    padding: 12px 20px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.dropdown-divider {
    margin: 0;
    border-top: 1px solid #dee2e6;
}

/* Notification Item Styles */
.notification-item {
    cursor: pointer;
    padding: 12px 20px;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.notification-item:hover {
    background-color: #e9f0ff;
}

.notification-item.unread {
    background-color: #d0e2ff;
}

.notification-icon {
    font-size: 1.5rem;
    color: #22c55e;
    flex-shrink: 0;
}

.notification-title {
    font-weight: 700;
    font-size: 1rem;
    margin-bottom: 3px;
    color: #222;
}

.notification-message {
    font-size: 0.85rem;
    color: #555;
    white-space: pre-line;
}

.notification-time {
    font-size: 0.75rem;
    color: #888;
    margin-top: 6px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.notification-dot {
    width: 10px;
    height: 10px;
    background: #d10d17;
    border-radius: 50%;
    margin-left: 8px;
    flex-shrink: 0;
}

/* Permission Alert Styles */
#permissionAlert {
    font-size: 0.9rem;
    background: #fff3cd;
    border-color: #ffeeba;
    color: #856404;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    border-radius: 8px;
    margin: 12px 16px;
}

#permissionAlert i {
    font-size: 1.3rem;
}

#enableNotificationsBtn {
    margin-left: auto;
}

/* View All Notifications Button */
.dropdown-item-text .btn {
    width: 100%;
    font-weight: 600;
    border-radius: 8px;
}

/* Loading Spinner */
#notificationsList .spinner-border {
    color: #22c55e;
}

/* Alert Styles */
.alert-sm {
    padding: 0.5rem;
    font-size: 0.875rem;
}

/* Animations */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}
