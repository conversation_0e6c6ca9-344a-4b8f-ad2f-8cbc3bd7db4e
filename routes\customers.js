const express = require('express');
const router = express.Router();
const CustomerApiController = require('../controllers/CustomerApiController');
const CustomersController = require('../controllers/CustomersController');
const { uploadCustomers, handleMulterError } = require('../middleware/upload');
const { authenticateCustomer,authenticateDriver, rateLimiter ,optionalAuth} = require('../middleware/apiAuth');
 
// Rate limiting for auth endpoints
const authLimiter = rateLimiter(5, 15 * 60 * 1000); // 5 attempts per 15 minutes
// تسجيل دخول العميل
router.post('/login', CustomerApiController.login);

// تسجيل عميل جديد
router.post('/register', CustomerApiController.register);

// الحصول على بيانات العميل
router.get('/profile',authenticateCustomer, CustomerApiController.getProfile.bind(CustomerApiController));

// تحديث بيانات العميل
router.put('/profile', authenticateCustomer,uploadCustomers.single('image'), CustomerApiController.updateProfile.bind(CustomerApiController));

// تغيير كلمة المرور
router.put('/change-password', authenticateCustomer, CustomerApiController.changePassword);

// تسجيل الخروج
router.post('/logout', authenticateCustomer, CustomerApiController.logout);

// الصفحة الرئيسية (عامة - لا تحتاج مصادقة)
router.get('/Home', CustomersController.getHome);
router.get('/categories', CustomersController.getCategory);
router.get('/categories/:id', CustomersController.getProductsByCategory);// متاجر حسب الفئة (عامة)
router.get('/products', CustomersController.getProducts);// تفاصيل منتج (عامة)
router.get('/products/:id', CustomersController.getProductDetails);// تفاصيل منتج (عامة)


router.post('/checkout',authenticateCustomer, CustomersController.processAddCheckout.bind(CustomersController));// معالجة الدفع وإنشاء الطلب
router.get('/orders',authenticateCustomer, CustomersController.getOrders);// الحصول على طلبات العميل
router.get('/orders/completed',authenticateCustomer, CustomersController.getOrdersCompleted);// تفاصيل طلب (ع
router.post('/orders/:id',authenticateCustomer, CustomersController.completedorders.bind(CustomersController));// الحصول على طلبات العميل
router.post('/orders/:id/cancel',authenticateCustomer, CustomersController.canceledorders.bind(CustomersController));// الحصول على طلبات العميل


router.get('/delivery',authenticateDriver, CustomersController.getDelivery);
router.get('/delivery/completed',authenticateDriver, CustomersController.getDeliverycompleted);
router.get('/delivery/:id',authenticateDriver, CustomersController.getDeliveryDetails);
router.get('/profiledriver',authenticateDriver, CustomersController.getProfileDriver);
router.put('/change-passworddriver',authenticateDriver, CustomersController.changePasswordDriver);
// التحقق من صحة التوكن
router.get('/verify-token', authenticateCustomer, (req, res) => {
    res.json({
        success: true,
        message: 'التوكن صحيح',
        data: {
            customer: {
                id: req.customer.id,
                name: req.customer.name,
                email: req.customer.email,
                phone: req.customer.phone
            }
        }
    });
});

// تجديد التوكن
router.post('/refresh-token', authenticateCustomer, (req, res) => {
    const jwt = require('jsonwebtoken');

    try {
        // إنشاء توكن جديد
        const newToken = jwt.sign(
            {
                customerId: req.customer.id,
                email: req.customer.email,
                type: 'customer'
            },
            process.env.JWT_SECRET || 'your-secret-key',
            { expiresIn: '30d' }
        );

        res.json({
            success: true,
            message: 'تم تجديد التوكن بنجاح',
            data: { token: newToken }
        });

    } catch (error) {
        console.error('خطأ في تجديد التوكن:', error);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ في الخادم',
            data: null
        });
    }
});

// حفظ توكن Firebase للعميل
router.post('/save-firebase-token', authenticateCustomer, CustomersController.saveFirebaseToken.bind(CustomersController));

router.post('/delivery/save-firebase-token', authenticateDriver, CustomersController.saveTokendriver.bind(CustomersController));

module.exports = router;
