const express = require('express');
const router = express.Router();
const CustomerAuthController = require('../controllers/CustomerAuthController');
const CustomersController = require('../controllers/CustomersController');
const ProductsCustomerController = require('../controllers/ProductsCustomerController');
const OrdersCustomerController = require('../controllers/OrdersCustomerController');
const DeliveryCustomersController = require('../controllers/DeliveryCustomersController');
const { uploadCustomers, handleMulterError } = require('../middleware/upload');
const { authenticateCustomer,authenticateDriver, rateLimiter ,optionalAuth} = require('../middleware/apiAuth');
 
// Rate limiting for auth endpoints
const authLimiter = rateLimiter(5, 15 * 60 * 1000); // 5 attempts per 15 minutes
// تسجيل دخول العميل
router.post('/login', CustomerAuthController.login);

// تسجيل عميل جديد
router.post('/register', CustomerAuthController.register);

// الحصول على بيانات العميل
router.get('/profile',authenticateCustomer, CustomerAuthController.getProfile.bind(CustomerAuthController));

// تحديث بيانات العميل
router.put('/profile', authenticateCustomer,uploadCustomers.single('image'), CustomerAuthController.updateProfile.bind(CustomerAuthController));

// تغيير كلمة المرور
router.put('/change-password', authenticateCustomer, CustomerAuthController.changePassword);

// تسجيل الخروج
router.post('/logout', authenticateCustomer, CustomerAuthController.logout);

// الصفحة الرئيسية (عامة - لا تحتاج مصادقة)
router.get('/Home', CustomersController.getHome);
router.get('/categories', ProductsCustomerController.getCategory);
router.get('/categories/:id', ProductsCustomerController.getProductsByCategory);// متاجر حسب الفئة (عامة)
router.get('/products', ProductsCustomerController.getProducts);// تفاصيل منتج (عامة)
router.get('/products/:id', ProductsCustomerController.getProductDetails);// تفاصيل منتج (عامة)


router.post('/checkout',authenticateCustomer, OrdersCustomerController.processAddCheckout.bind(OrdersCustomerController));// معالجة الدفع وإنشاء الطلب
router.get('/orders',authenticateCustomer, OrdersCustomerController.getOrders);// الحصول على طلبات العميل
router.get('/orders/completed',authenticateCustomer, OrdersCustomerController.getOrdersCompleted);// تفاصيل طلب (ع
router.post('/orders/:id',authenticateCustomer, OrdersCustomerController.completedorders.bind(OrdersCustomerController));// الحصول على طلبات العميل
router.post('/orders/:id/cancel',authenticateCustomer, OrdersCustomerController.canceledorders.bind(OrdersCustomerController));// الحصول على طلبات العميل


router.get('/delivery',authenticateDriver, DeliveryCustomersController.getDelivery);
router.get('/delivery/completed',authenticateDriver, DeliveryCustomersController.getDeliverycompleted);
router.get('/delivery/:id',authenticateDriver, DeliveryCustomersController.getDeliveryDetails);

router.get('/profiledriver',authenticateDriver, CustomerAuthController.getProfileDriver);
router.put('/change-passworddriver',authenticateDriver, CustomerAuthController.changePasswordDriver.bind(CustomerAuthController));
// التحقق من صحة التوكن
router.get('/verify-token', authenticateCustomer, (req, res) => {
    res.json({
        success: true,
        message: 'التوكن صحيح',
        data: {
            customer: {
                id: req.customer.id,
                name: req.customer.name,
                email: req.customer.email,
                phone: req.customer.phone
            }
        }
    });
});

// تجديد التوكن
router.post('/refresh-token', authenticateCustomer, (req, res) => {
    const jwt = require('jsonwebtoken');

    try {
        // إنشاء توكن جديد
        const newToken = jwt.sign(
            {
                customerId: req.customer.id,
                email: req.customer.email,
                type: 'customer'
            },
            process.env.JWT_SECRET || 'your-secret-key',
            { expiresIn: '30d' }
        );

        res.json({
            success: true,
            message: 'تم تجديد التوكن بنجاح',
            data: { token: newToken }
        });

    } catch (error) {
        console.error('خطأ في تجديد التوكن:', error);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ في الخادم',
            data: null
        });
    }
});

// حفظ توكن Firebase للعميل
router.post('/save-firebase-token', authenticateCustomer, CustomerAuthController.saveFirebaseToken.bind(CustomersController));

router.post('/delivery/save-firebase-token', authenticateDriver, CustomerAuthController.saveTokendriver.bind(CustomersController));

module.exports = router;
