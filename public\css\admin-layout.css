/* Admin Layout Styles - Professional CSS Organization */

/* CSS Custom Properties (Variables) */
:root {
    --primary-color: #B2CD9C;
    --secondary-color: #8FBC8F;
    --accent-color: #7BA05B;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --bg-light: #f8f9fa;
    --white: #ffffff;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Global Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, var(--bg-light) 0%, #e8f5e8 100%);
    color: var(--text-dark);
    line-height: 1.6;
}

/* Navigation Styles */
.navbar-custom {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    box-shadow: var(--shadow);
    padding: 1rem 0;
}

.navbar-brand {
    color: var(--white) !important;
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem !important;
    border-radius: 8px;
    margin: 0 0.2rem;
}

.nav-link:hover {
    color: var(--white) !important;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white) !important;
}

/* Layout Components */
.container-main {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.welcome-card {
    background: var(--white);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
    border-left: 5px solid var(--primary-color);
}

/* Grid Layouts */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* Card Components */
.stat-card {
    background: var(--white);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.action-card {
    background: var(--white);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.action-card h5 {
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-weight: 600;
}

/* Stat Components */
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-light);
    font-size: 1rem;
    font-weight: 500;
}

/* Button Styles */
.btn-custom {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    color: var(--white);
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-bottom: 0.5rem;
    width: 100%;
}

.btn-custom:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
    color: var(--white);
}

/* Dropdown Styles */
.dropdown-item:hover {
    background: var(--bg-light);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-main {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .actions-grid {
        grid-template-columns: 1fr;
    }
}
