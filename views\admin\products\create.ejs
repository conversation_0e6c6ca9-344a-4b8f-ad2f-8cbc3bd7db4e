<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة منتج جديد - نظام إدارة المتجر</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #B2CD9C;
            --secondary-color: #8FBC8F;
            --accent-color: #7BA05B;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --bg-light: #f8f9fa;
            --white: #ffffff;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--bg-light) 0%, #e8f5e8 100%);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .navbar-custom {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: var(--shadow);
            padding: 1rem 0;
        }

        .navbar-brand {
            color: var(--white) !important;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem !important;
            border-radius: 8px;
            margin: 0 0.2rem;
        }

        .nav-link:hover {
            color: var(--white) !important;
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: var(--white) !important;
        }

        .container-main {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
        }

        .form-card {
            background: var(--white);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow);
            border-left: 5px solid var(--primary-color);
        }

        .form-label {
            color: var(--text-dark);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(178, 205, 156, 0.25);
        }

        .btn-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: var(--white);
            padding: 0.75rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
            color: var(--white);
        }

        .btn-secondary-custom {
            background: #6c757d;
            border: none;
            color: var(--white);
            padding: 0.75rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-secondary-custom:hover {
            background: #5a6268;
            transform: translateY(-2px);
            color: var(--white);
        }

        .file-upload-area {
            border: 2px dashed var(--primary-color);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            background: rgba(178, 205, 156, 0.1);
            transition: all 0.3s ease;
        }

        .file-upload-area:hover {
            background: rgba(178, 205, 156, 0.2);
        }

        .file-upload-wrapper input[type="file"] {
            border: 2px dashed var(--primary-color);
            border-radius: 12px;
            padding: 15px;
            background: rgba(178, 205, 156, 0.05);
            transition: all 0.3s ease;
        }

        .file-upload-wrapper input[type="file"]:hover {
            background: rgba(178, 205, 156, 0.15);
            border-color: var(--secondary-color);
        }

        .file-upload-wrapper input[type="file"]:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(178, 205, 156, 0.25);
            background: rgba(178, 205, 156, 0.1);
        }

        .alert-custom {
            border: none;
            border-radius: 10px;
            padding: 1rem 1.5rem;
        }

        .alert-danger {
            background: rgba(231, 76, 60, 0.1);
            color: #c0392b;
            border-left: 4px solid #e74c3c;
        }

        @media (max-width: 768px) {
            .container-main {
                padding: 1rem;
            }
            
            .form-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="/admin/dashboard">
                <i class="fas fa-store me-2"></i>
                نظام إدارة المتجر
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/customers">
                            <i class="fas fa-users me-1"></i>العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/products">
                            <i class="fas fa-box me-1"></i>المنتجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/categories">
                            <i class="fas fa-tags me-1"></i>الفئات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/orders">
                            <i class="fas fa-shopping-cart me-1"></i>الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/deliveries">
                            <i class="fas fa-truck me-1"></i>التوصيلات
                        </a>
                    </li>
                </ul>
                
                <div class="dropdown">
                    <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        المدير
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/admin/profile">
                            <i class="fas fa-user me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="/admin/settings">
                            <i class="fas fa-cog me-2"></i>الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="/admin/auth/logout">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-main">
        <div class="form-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 style="color: var(--primary-color);">
                        <i class="fas fa-plus-circle me-2"></i>
                        إضافة منتج جديد
                    </h2>
                    <p class="text-muted mb-0">أضف منتج جديد إلى المتجر</p>
                </div>
                <a href="/admin/products" class="btn btn-secondary-custom">
                    <i class="fas fa-arrow-right me-2"></i>العودة للمنتجات
                </a>
            </div>

            <% if (typeof error !== 'undefined' && error) { %>
                <div class="alert alert-danger alert-custom mb-4">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <%= error %>
                </div>
            <% } %>

            <form action="/admin/products" method="POST" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="name" class="form-label">
                                <i class="fas fa-tag me-2"></i>اسم المنتج *
                            </label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<%= typeof formData !== 'undefined' ? formData.name || '' : '' %>" 
                                   required placeholder="أدخل اسم المنتج">
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">
                                <i class="fas fa-align-left me-2"></i>وصف المنتج
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                      placeholder="أدخل وصف المنتج"><%= typeof formData !== 'undefined' ? formData.description || '' : '' %></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="barcode" class="form-label">
                                <i class="fas fa-barcode me-2"></i>الباركود (اختياري)
                            </label>
                            <input type="text" class="form-control" id="barcode" name="barcode"
                                   value="<%= typeof formData !== 'undefined' ? formData.barcode || '' : '' %>"
                                   placeholder="أدخل رقم الباركود">
                            <small class="form-text text-muted">اتركه فارغاً إذا لم يكن متوفراً</small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">
                                        <i class="fas fa-money-bill me-2"></i>السعر (ر.س) *
                                    </label>
                                    <input type="number" class="form-control" id="price" name="price"
                                           value="<%= typeof formData !== 'undefined' ? formData.price || '' : '' %>"
                                           step="0.01" min="0" required placeholder="0.00">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quantity" class="form-label">
                                        <i class="fas fa-boxes me-2"></i>الكمية المتاحة *
                                    </label>
                                    <input type="number" class="form-control" id="quantity" name="quantity"
                                           value="<%= typeof formData !== 'undefined' ? formData.quantity || '' : '' %>"
                                           min="0" required placeholder="0">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="categoryId" class="form-label">
                                <i class="fas fa-list me-2"></i>الفئة *
                            </label>
                            <select class="form-select" id="categoryId" name="categoryId" required>
                                <option value="">اختر الفئة</option>
                                <% if (typeof categories !== 'undefined' && categories) { %>
                                    <% categories.forEach(category => { %>
                                        <option value="<%= category.id %>" 
                                                <%= typeof formData !== 'undefined' && formData.categoryId == category.id ? 'selected' : '' %>>
                                            <%= category.name %>
                                        </option>
                                    <% }); %>
                                <% } %>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="images" class="form-label">
                                <i class="fas fa-images me-2"></i>صور المنتج
                            </label>
                            <div class="file-upload-wrapper">
                                <input type="file" class="form-control" id="images" name="images"
                                       multiple accept="image/*">
                                <small class="form-text text-muted">يمكنك اختيار عدة صور (حد أقصى 10 صور، 5MB لكل صورة)</small>
                            </div>
                            <div id="preview-container" class="mt-3" style="display: none;">
                                <h6>الصور المختارة:</h6>
                                <div id="image-previews" class="d-flex flex-wrap gap-2"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-3 mt-4">
                    <a href="/admin/products" class="btn btn-secondary-custom">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </a>
                    <button type="submit" class="btn btn-custom">
                        <i class="fas fa-save me-2"></i>حفظ المنتج
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // معاينة الصور المختارة
        document.getElementById('images').addEventListener('change', function(e) {
            const files = e.target.files;
            const previewContainer = document.getElementById('preview-container');
            const imagePreviews = document.getElementById('image-previews');

            // مسح المعاينات السابقة
            imagePreviews.innerHTML = '';

            if (files.length > 0) {
                previewContainer.style.display = 'block';

                // عرض كل صورة
                Array.from(files).forEach((file, index) => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const imageDiv = document.createElement('div');
                            imageDiv.className = 'position-relative me-2 mb-2';
                            imageDiv.innerHTML = `
                                <img src="${e.target.result}" alt="معاينة ${index + 1}"
                                     style="width: 100px; height: 100px; object-fit: cover; border-radius: 8px; border: 2px solid var(--primary-color);">
                                <small class="d-block text-center mt-1">${file.name}</small>
                            `;
                            imagePreviews.appendChild(imageDiv);
                        };
                        reader.readAsDataURL(file);
                    }
                });
            } else {
                previewContainer.style.display = 'none';
            }
        });
    </script>
</body>
</html>
