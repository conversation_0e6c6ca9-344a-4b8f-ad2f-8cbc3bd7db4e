<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="connect-src 'self' https://nominatim.openstreetmap.org https://www.googleapis.com http://firebaseinstallations.googleapis.com https://fcmregistrations.googleapis.com;">

    <title>لوحة الإدارة - نظام إدارة المتجر الذكي</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛒</text></svg>">

    <!-- Meta Tags -->
    <meta name="description" content="لوحة إدارة نظام المتجر الذكي">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Animate.css -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="/css/admin.css" rel="stylesheet">

    <!-- Notification Styles -->
    <style>
       :root {
            --primary-color: #0ea5e9;           /* اللون السماوي الأساسي */
            --secondary-color: #0284c7;         /* سماوي أغمق قليلاً */
            --accent-color: #38bdf8;            /* سماوي فاتح للتأكيد */
            --success-color: #10b981;           /* نجاح */
            --warning-color: #f59e0b;           /* تحذير */
            --dark-color: #0c4a6e;              /* سماوي داكن */
            --light-color: #f0f9ff;             /* سماوي فاتح جداً */
            --white: #ffffff;
            --text-dark: #0c4a6e;
            --text-light: #0369a1;
            --bg-light: #f0f9ff;
            --shadow: 0 8px 25px rgba(14, 165, 233, 0.15);
            --shadow-lg: 0 20px 40px rgba(14, 165, 233, 0.25);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #bae6fd 50%, #7dd3fc 75%, #38bdf8 100%);
            background-attachment: fixed;
            color: var(--text-dark);
            line-height: 1.6;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(56, 189, 248, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(125, 211, 252, 0.05) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(10px) rotate(-1deg); }
        }

        .navbar-custom {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--dark-color) 100%);
            box-shadow:
                0 8px 32px rgba(14, 165, 233, 0.2),
                0 4px 16px rgba(56, 189, 248, 0.1);
            padding: 1rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .navbar-custom::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: slide 4s ease-in-out infinite;
        }

        @keyframes slide {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }

        .navbar-brand {
            color: var(--white) !important;
            font-weight: 700;
            font-size: 1.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .navbar-brand:hover {
            transform: scale(1.05);
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 0.75rem 1rem !important;
            border-radius: 10px;
            margin: 0 0.25rem;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .nav-link:hover {
            color: var(--white) !important;
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.25);
            color: var(--white) !important;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .container-main {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .welcome-card {
            background: var(--white);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            border-left: 5px solid var(--primary-color);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--white);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--white);
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-light);
            font-size: 1rem;
            font-weight: 500;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .action-card {
            background: var(--white);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
        }

        .action-card h5 {
            color: var(--text-dark);
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .btn-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: var(--white);
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 0.5rem;
            width: 100%;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
            color: var(--white);
        }

     

        .dropdown-item:hover {
            background: var(--bg-light);
        }

        /* Notification Styles */
      
      
       

        @media (max-width: 768px) {
            .container-main {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
    
        .notification-status-indicator {
            position: static;
            top: -2px;
            right: -2px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
            background-color: #dc3545; /* Red by default (disabled) */
            transition: background-color 0.3s ease;
        }

        .notification-status-indicator.enabled {
            background-color: #4bf773; /* Green when enabled */
        }

        .notification-status-indicator.pending {
            background-color: #ffc107; /* Yellow when pending */
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: bold;
            min-width: 18px;
            text-align: center;
            animation: bounce 0.5s ease-in-out;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-5px); }
            60% { transform: translateY(-3px); }
        }

        .alert-sm {
            padding: 0.5rem;
            font-size: 0.875rem;
        }
        .notification-status-dot {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #dc3545;
            transition: background-color 0.3s ease;
        }

        .notification-status-dot.enabled {
            background-color: #28a745;
        }

        .notification-status-dot.pending {
            background-color: #ffc107;
            animation: pulse 1.5s infinite;
        }
        
        /* زر الجرس مع العدد */
        #notificationDropdown {
            position: relative;
            font-size: 1.2rem;
        }
        #notificationDropdown .fa-bell {
            color: #fff;
            transition: color 0.3s ease;
        }
        #notificationDropdown:hover .fa-bell {
            color: #22c55e; /* لون أزرق Bootstrap */
        }

        /* النقطة الصغيرة للإشعارات الجديدة */
        #notificationStatusDot {
            position: absolute;
            top: 6px;
            right: 8px;
            width: 10px;
            height: 10px;
            background: #dc3545; /* أحمر */
            border-radius: 50%;
            border: 2px solid white;
            display: none; /* تظهر فقط عند وجود إشعارات جديدة */
            z-index: 10;
        }

        /* علامة عدد الإشعارات */
        #notificationBadge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #d10d17;
            color: white;
            font-size: 0.65rem;
            font-weight: 700;
            min-width: 18px;
            height: 18px;
            line-height: 18px;
            border-radius: 9px;
            text-align: center;
            padding: 0 5px;
            z-index: 10;
        }

            /* وضع القائمة بشكل مطلق فوق كل شيء */
        .dropdown-menu {
            width: 360px !important;
            position: absolute !important;
            top: 100% !important;  /* يضمن أنها تظهر تحت الزر */
            left: auto !important;  /* أو حسب المحاذاة */
            right: 0 !important;    /* لمحاذاة القائمة لليمين */
            max-height: 400px;      /* ارتفاع قابل للتمرير */
            overflow-y: auto;       /* تفعيل التمرير العمودي */
            z-index: 9999 !important; /* تأكد أنها فوق كل شيء */
            background: #f9f9f9;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            padding: 0;
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            text-align: right;
        }

        .dropdown-admin {
            width: 150px !important;
        }

        .dropdown-header {
            background: #22c55e;
            color: white;
            font-weight: 600;
            padding: 12px 20px;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
        }

        /* تقسيمات */
        .dropdown-divider {
            margin: 0;
            border-top: 1px solid #dee2e6;
        }

        /* تنسيق عنصر الإشعار */
        .notification-item {
            cursor: pointer;
            padding: 12px 20px;
            transition: background-color 0.3s ease;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }
        .notification-item:hover {
            background-color: #e9f0ff;
        }
        .notification-item.unread {
            background-color: #d0e2ff;
        }

        .notification-icon {
            font-size: 1.5rem;
            color: #22c55e;
            flex-shrink: 0;
        }

        .notification-title {
            font-weight: 700;
            font-size: 1rem;
            margin-bottom: 3px;
            color: #222;
        }

        .notification-message {
            font-size: 0.85rem;
            color: #555;
            white-space: pre-line;
        }

        .notification-time {
            font-size: 0.75rem;
            color: #888;
            margin-top: 6px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .notification-dot {
            width: 10px;
            height: 10px;
            background: #d10d17;
            border-radius: 50%;
            margin-left: 8px;
            flex-shrink: 0;
        }

        /* تنسيق تنبيه السماح بالإشعارات */
        #permissionAlert {
            font-size: 0.9rem;
            background: #fff3cd;
            border-color: #ffeeba;
            color: #856404;
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 16px;
            border-radius: 8px;
            margin: 12px 16px;
        }

        #permissionAlert i {
            font-size: 1.3rem;
        }

        #enableNotificationsBtn {
            margin-left: auto;
        }

        /* زر عرض جميع الإشعارات */
        .dropdown-item-text .btn {
            width: 100%;
            font-weight: 600;
            border-radius: 8px;
        }

        /* Spinner صغير */
        #notificationsList .spinner-border {
            color: #22c55e;
        }

        /* السماح للـ navbar بأن تظهر القوائم خارجه */
        .navbar, .navbar-nav {
        overflow: visible !important;
        position: relative !important; /* لو عندك position ثابت أو absolute جرب تغيره */
        }


    </style>
    
    <!-- Chart.js for Analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Firebase for Notifications -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-messaging-compat.js"></script>
    <script src="/firebase-config.js"></script>
</head>
<body>
    <!-- Sidebar Toggle Button (Mobile) -->
    <button class="sidebar-toggle" id="sidebarToggle">
        <i class="fas fa-bars"></i>
    </button>

     <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="/admin/dashboard">
                <i class="fas fa-store me-2"></i>
                نظام إدارة المتجر
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/customers">
                            <i class="fas fa-users me-1"></i>العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/products">
                            <i class="fas fa-box me-1"></i>المنتجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/categories">
                            <i class="fas fa-tags me-1"></i>الفئات
                        </a>
                    </li>
                     <li class="nav-item">
                        <a class="nav-link" href="/admin/offers">
                            <i class="fas fa-tags me-1"></i>العروض
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/orders">
                            <i class="fas fa-box-open me-1"></i>الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/orders/pending">
                            <i class="fas fa-clock me-1"></i>الطلبات المعلقة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/drivers">
                            <i class="fas fa-truck me-1"></i>السائقين
                        </a>
                    </li>
                      <li class="nav-item">
                        <a class="nav-link" href="/admin/deliveries">
                            <i class="fas fa-truck me-1"></i>التوصيلات
                        </a>
                    </li>
                    <li class="nav-item position-relative">
                        <a class="nav-link" href="/admin/notifications">
                            <i class="fas fa-bell me-1"></i>الإشعارات
                            <span class="notification-status-dot" id="sidebarNotificationDot"></span>
                        </a>
                    </li>
                </ul>

                <!-- Notifications Dropdown -->
               <div class="dropdown me-3">
                <button class="btn btn-outline-light btn-sm position-relative" type="button" data-bs-toggle="dropdown" id="notificationDropdown" aria-expanded="false" aria-haspopup="true" aria-label="فتح قائمة الإشعارات">
                    <i class="fas fa-bell"></i>
                    <span class="notification-status-indicator" id="notificationStatusDot" aria-hidden="true"></span>
                    <span class="notification-badge d-none" id="notificationBadge" aria-live="polite" aria-atomic="true">0</span>
                </button>
                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationDropdown" role="menu" style="max-height: 400px; overflow-y: auto;">
                    <div class="dropdown-header d-flex justify-content-between align-items-center">
                        <span>الإشعارات</span>
                        <small class="text-white" id="notificationStatus">جاري التحميل...</small>
                    </div>
                    <div class="dropdown-divider"></div>

                    <!-- Permission Alert -->
                    <div class="alert alert-warning alert-sm m-2 d-none" id="permissionAlert" role="alert">
                        <i class="fas fa-exclamation-triangle"></i>
                        يرجى السماح بالإشعارات لتلقي التحديثات
                        <button class="btn btn-sm btn-warning mt-1" id="enableNotificationsBtn" onclick="window.adminNotifications.requestNotificationPermission()">
                            <i class="fas fa-bell me-1"></i>
                            تفعيل الإشعارات
                        </button>
                    </div>

                    <!-- Notifications List -->
                    <div id="notificationsList">
                        <div class="text-center p-3">
                            <div class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></div>
                            <div class="mt-2">جاري تحميل الإشعارات...</div>
                        </div>
                    </div>

                    <div class="dropdown-divider"></div>
                    <div class="dropdown-item-text text-center">
                        <a href="/admin/notifications" class="btn btn-sm btn-primary" role="button">
                            عرض جميع الإشعارات
                        </a>
                    </div>
                </div>
                </div>

                <div class="dropdown">
                    <button
                        class="btn btn-outline-light btn-sm dropdown-toggle"
                        type="button"
                        id="adminDropdown"
                        data-bs-toggle="dropdown"
                        aria-expanded="false"
                    >
                        <i class="fas fa-user-circle me-1"></i> المدير
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end dropdown-admin" aria-labelledby="adminDropdown">
                        <li>
                        <a class="dropdown-item" href="/admin/profile">
                            <i class="fas fa-user me-2"></i> الملف الشخصي
                        </a>
                        </li>
                        <li>
                        <a class="dropdown-item" href="/admin/notifications">
                            <i class="fas fa-bell me-2"></i> الإشعارات
                        </a>
                        </li>
                        <li>
                        <a class="dropdown-item" href="/admin/settings">
                            <i class="fas fa-cog me-2"></i> الإعدادات
                        </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                        <a class="dropdown-item text-danger" href="/admin/auth/logout">
                            <i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج
                        </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content Area -->
    <main class="container-main">
        <%- body %>
    </main>

    <div id="admin-notifications" style="position: fixed; top: 200px; right: 10px; z-index: 1000;"></div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <!-- <script src="/js/admin.js"></script> تم تعطيله لتجنب التضارب مع نظام الإشعارات المدمج -->

    <!-- Page-specific scripts -->
    <% if (locals.pageScripts) { %>
        <%- pageScripts %>
    <% } %>
    
    <script>
        const toggleBtn = document.querySelector('.toggle-btn');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', function() {
                this.nextElementSibling.classList.toggle('show');
            });
        }
    </script>

<!-- 2. كود تهيئة Firebase والرسائل -->
<script>
  const firebaseConfig = {
    apiKey: "AIzaSyBdtHd9YCMgqvBs0KAVaen3Jg4M6x2mdhE",
    authDomain: "company-firebase-77daf.firebaseapp.com",
    projectId: "company-firebase-77daf",
    storageBucket: "company-firebase-77daf.appspot.com",
    messagingSenderId: "76415949840",
    appId: "1:76415949840:web:d74b24b187a1abf392fe95",
    measurementId: "G-YRPEE91G0C"
  };

  // تهيئة Firebase
  let messaging;
  try {
    messaging = window.initializeFirebaseMessaging();
    console.log("✅ تم تهيئة Firebase Messaging بنجاح");
  } catch (initError) {
    console.error("❌ فشل في تهيئة Firebase:", initError);
    firebase.initializeApp(firebaseConfig);
    messaging = firebase.messaging();
  }

  window.addEventListener('load', async () => {
    try {
      console.log("🔄 بدء تفعيل الإشعارات التلقائي...");

      // فحص دعم المتصفح
      if (!('Notification' in window)) {
        throw new Error('هذا المتصفح لا يدعم الإشعارات');
      }

      if (!('serviceWorker' in navigator)) {
        throw new Error('هذا المتصفح لا يدعم Service Workers');
      }

      if (!firebase || !firebase.messaging) {
        throw new Error('Firebase Messaging غير محمل بشكل صحيح');
      }

      console.log("✅ دعم المتصفح جاهز");

      // طلب الإذن مباشرة
      const permission = await Notification.requestPermission();
      if (permission !== 'granted') {
        throw new Error('تم رفض إذن الإشعارات. يرجى السماح بها من إعدادات المتصفح.');
      }

      console.log("✅ تم منح إذن الإشعارات");

      // تسجيل Service Worker
      let registration;
      try {
        const existingRegistrations = await navigator.serviceWorker.getRegistrations();
        for (const reg of existingRegistrations) {
          if (reg.scope.includes('firebase-messaging-sw')) {
            await reg.unregister();
            console.log("🗑️ تم إلغاء تسجيل Service Worker قديم");
          }
        }

        registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
          scope: '/',
          updateViaCache: 'none'
        });

        console.log("✅ تم تسجيل Service Worker بنجاح");
        await navigator.serviceWorker.ready;

      } catch (swError) {
        console.error("❌ فشل تسجيل Service Worker:", swError);
        throw new Error(`فشل تسجيل Service Worker: ${swError.message}`);
      }

      // الحصول على FCM Token
      let currentToken;
      try {
        currentToken = await messaging.getToken({
          vapidKey: window.VAPID_KEY || 'BBH3EJRa-TWHRVwbD1dmtBbmdfzf3SMi-NA8Zey9jW5LM-m5o5mTNUqTkiKMu6Iwn9dvrhEA69f0ZdL_gPuoJVc',
          serviceWorkerRegistration: registration
        });
      } catch (tokenError) {
        console.error("❌ فشل في توليد التوكن:", tokenError);
        throw new Error('فشل في توليد التوكن');
      }

      if (!currentToken) {
        throw new Error('لم يتم الحصول على توكن صالح');
      }

      console.log("✅ FCM Token:", currentToken.substring(0, 20) + "...");

      // حفظ التوكن بالسيرفر
      const response = await fetch('/admin/savetoken', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token: currentToken })
      });

      if (!response.ok) {
        throw new Error(`فشل في حفظ التوكن بالسيرفر (${response.status})`);
      }

      console.log("✅ تم حفظ التوكن بنجاح");

    } catch (error) {
      console.error("❌ حدث خطأ أثناء تفعيل الإشعارات:", error);
    }
  });

</script>
    <!-- Firebase Notifications Script -->
    <script>
        // Global notification system for admin panel
        class AdminNotificationSystem {
            constructor() {
                this.messaging = null;
                this.isInitialized = false;
                this.isEnabled = false;
                this.notificationCount = 0;
                this.notifications = [];
                this.isUpdating = false;
                this.notificationsLoaded = false;
                this.updateInterval = null;
                this.init();
            }

            async init() {
                try {
                    console.log('🔔 تهيئة نظام الإشعارات للإدمن...');

                    // Check if Firebase is loaded
                    if (typeof firebase === 'undefined') {
                        console.error('❌ Firebase غير محمل');
                        this.updateNotificationStatus('error');
                        return;
                    }

                    // Initialize Firebase messaging
                    if (window.initializeFirebaseMessaging) {
                        this.messaging = window.initializeFirebaseMessaging();
                    } else {
                        // Fallback
                        firebase.initializeApp(window.FIREBASE_CONFIG || {
                            apiKey: "AIzaSyBdtHd9YCMgqvBs0KAVaen3Jg4M6x2mdhE",
                            authDomain: "company-firebase-77daf.firebaseapp.com",
                            projectId: "company-firebase-77daf",
                            storageBucket: "company-firebase-77daf.appspot.com",
                            messagingSenderId: "76415949840",
                            appId: "1:76415949840:web:d74b24b187a1abf392fe95"
                        });
                        this.messaging = firebase.messaging();
                    }

                    // Check notification permission
                    await this.checkNotificationPermission();

                    // Setup message handlers
                    this.setupMessageHandlers();

                    // Setup UI event handlers
                    this.setupUIHandlers();

                    // تحميل قائمة الإشعارات
                    this.loadNotificationsList();

                    // تحديث قائمة الإشعارات كل 30 ثانية (فقط إذا لم يكن هناك تحديث جاري)
                    this.updateInterval = setInterval(() => {
                        if (!this.isUpdating) {
                            this.loadNotificationsList();
                        }
                    }, 30000);

                    this.isInitialized = true;
                    console.log('✅ تم تهيئة نظام الإشعارات بنجاح');

                } catch (error) {
                    console.error('❌ فشل في تهيئة نظام الإشعارات:', error);
                    this.updateNotificationStatus('error');
                }
            }

            async checkNotificationPermission() {
                if (!('Notification' in window)) {
                    console.warn('⚠️ المتصفح لا يدعم الإشعارات');
                    this.updateNotificationStatus('unsupported');
                    return;
                }

                const permission = Notification.permission;
                console.log('🔐 حالة إذن الإشعارات:', permission);

                switch (permission) {
                    case 'granted':
                        this.isEnabled = true;
                        this.updateNotificationStatus('enabled');
                        //await this.registerToken();
                        // إخفاء رسالة التفعيل إذا كانت ظاهرة
                        this.hidePermissionAlert();
                        break;
                    case 'denied':
                        this.isEnabled = false;
                        this.updateNotificationStatus('denied');
                        // تسجيل أن المستخدم رفض الإذن - لن تظهر الرسالة مرة أخرى
                        localStorage.setItem('notificationPromptShown', 'true');
                        localStorage.setItem('notificationPermissionDenied', 'true');
                        console.log('🔕 الإشعارات مرفوضة - لن يتم عرض رسالة التفعيل');
                        break;
                    case 'default':
                        this.isEnabled = false;
                        this.updateNotificationStatus('default');
                        // إظهار الرسالة فقط في الصفحة الرئيسية وإذا لم يتم عرضها مسبقاً
                        this.showPermissionAlert();
                        break;
                }
            }

            async enableNotifications() {
                try {
                    this.updateNotificationStatus('pending');

                    let token;
                    if (window.fixTokenIssues) {
                        token = await window.fixTokenIssues();
                    } else {
                        // Fallback method
                        const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
                        await navigator.serviceWorker.ready;

                        token = await this.messaging.getToken({
                            vapidKey: window.VAPID_KEY || 'BBH3EJRa-TWHRVwbD1dmtBbmdfzf3SMi-NA8Zey9jW5LM-m5o5mTNUqTkiKMu6Iwn9dvrhEA69f0ZdL_gPuoJVc',
                            serviceWorkerRegistration: registration
                        });
                    }

                    if (token) {
                        await this.saveTokenToServer(token);
                        this.updateNotificationStatus('enabled');
                        this.hidePermissionAlert();
                        console.log('✅ تم تفعيل الإشعارات بنجاح');

                        // Show success notification
                        this.showLocalNotification('تم تفعيل الإشعارات', 'سيتم إشعارك بالأحداث المهمة');
                    } else {
                        throw new Error('فشل في الحصول على التوكن');
                    }
                } catch (error) {
                    console.error('❌ فشل في تفعيل الإشعارات:', error);
                    this.updateNotificationStatus('error');
                    this.showError('فشل في تفعيل الإشعارات: ' + error.message);
                }
            }

            async saveTokenToServer(token) {
                const response = await fetch('/admin/savetoken', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ token })
                });

                if (!response.ok) {
                    throw new Error('فشل في حفظ التوكن على الخادم');
                }

                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.message || 'فشل في حفظ التوكن');
                }
            }

            setupMessageHandlers() {
                try {
                    // Handle foreground messages
                    this.messaging.onMessage((payload) => {
                        console.log('📨 تم استلام إشعار:', payload);
                        this.handleIncomingNotification(payload);
                    });

                    // Setup token refresh monitoring (Firebase v9+ compatible)
                    this.setupTokenRefreshMonitoring();

                } catch (error) {
                    console.error('❌ خطأ في إعداد معالجات الرسائل:', error);
                }
            }

            setupTokenRefreshMonitoring() {
                // Firebase v9+ لا يدعم onTokenRefresh
                // بدلاً من ذلك، نراقب التوكن بشكل دوري
                setInterval(async () => {
                    try {
                        if (!this.messaging) return;

                        const currentToken = await this.messaging.getToken({
                            vapidKey: this.vapidKey
                        });

                        if (currentToken && currentToken !== this.currentToken) {
                            console.log('🔄 تم اكتشاف توكن جديد');
                            this.currentToken = currentToken;
                            await this.saveTokenToServer(currentToken);
                            console.log('✅ تم تحديث التوكن بنجاح');
                        }
                    } catch (error) {
                        console.error('❌ خطأ في مراقبة تحديث التوكن:', error);
                    }
                }, 30 * 60 * 1000); // فحص كل 30 دقيقة
            }

            setupUIHandlers() {
                // Enable notifications button
                const enableBtn = document.getElementById('enableNotificationsBtn');
                const enableLink = document.getElementById('enableNotificationsLink');

                if (enableBtn) {
                    enableBtn.addEventListener('click', () => this.requestPermissionAndEnable());
                }

                if (enableLink) {
                    enableLink.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.requestPermissionAndEnable();
                    });
                }
            }

            async requestPermissionAndEnable() {
                try {
                    const permission = await Notification.requestPermission();
                    if (permission === 'granted') {
                        await this.enableNotifications();
                    } else {
                        this.updateNotificationStatus('denied');
                        this.showError('تم رفض إذن الإشعارات');
                    }
                } catch (error) {
                    console.error('❌ فشل في طلب إذن الإشعارات:', error);
                    this.showError('فشل في طلب إذن الإشعارات');
                }
            }

            handleIncomingNotification(payload) {
                const notification = {
                    id: Date.now(),
                    title: payload.notification?.title || 'إشعار جديد',
                    body: payload.notification?.body || '',
                    icon: payload.notification?.icon || '/logo.png',
                    timestamp: new Date(),
                    data: payload.data || {},
                    unread: true
                };

                // أضف للإشعارات المحلية
                this.notifications.unshift(notification);

                // حدث القائمة
                this.loadNotificationsList();

                // ✅ أظهر Toast داخل الصفحة
                this.showSuccessMessage(`${notification.title}: ${notification.body}`);

                // إشعار المتصفح في حالة الصفحة مخفية
                if (document.hidden) {
                    this.showLocalNotification(notification.title, notification.body, notification.icon);
                }

                if (payload.data?.type === "new_order") {
                    this.updateOrdersCounter();
                }
            }

            updateOrdersCounter() {
                const statsCards = document.querySelectorAll('.stat-card');
                statsCards.forEach((card) => {
                    const label = card.querySelector('.stat-label');
                    if (label && label.textContent.includes("إجمالي الطلبات")) {
                        const numberDiv = card.querySelector('.stat-number');
                        if (numberDiv) {
                            let current = parseInt(numberDiv.textContent) || 0;
                            numberDiv.textContent = current + 1;
                        }
                    }
                });
            }
            showLocalNotification(title, body, icon = '/logo.png') {
                if (Notification.permission === 'granted') {
                    new Notification(title, {
                        body: body,
                        icon: icon,
                        badge: '/logo.png',
                        tag: 'admin-notification'
                    });
                }
            }

            updateNotificationStatus(status) {
                const indicator = document.getElementById('notificationStatus');
                const sidebarDot = document.getElementById('sidebarNotificationStatus');

                // Update header indicator
                if (indicator) {
                    indicator.className = 'notification-status-indicator';

                    switch (status) {
                        case 'enabled':
                            indicator.classList.add('enabled');
                            indicator.title = 'الإشعارات مفعلة';
                            break;
                        case 'pending':
                            indicator.classList.add('pending');
                            indicator.title = 'جاري تفعيل الإشعارات...';
                            break;
                        case 'denied':
                        case 'error':
                            indicator.title = 'الإشعارات غير مفعلة';
                            break;
                        case 'unsupported':
                            indicator.title = 'المتصفح لا يدعم الإشعارات';
                            break;
                        default:
                            indicator.title = 'حالة الإشعارات غير معروفة';
                    }
                }

                // Update sidebar dot
                if (sidebarDot) {
                    sidebarDot.className = 'notification-status-dot';

                    switch (status) {
                        case 'enabled':
                            sidebarDot.classList.add('enabled');
                            break;
                        case 'pending':
                            sidebarDot.classList.add('pending');
                            break;
                    }
                }
            }

            showPermissionAlert() {
                // التحقق من أن الإشعارات مدعومة
                if (!('Notification' in window)) {
                    console.log('🔕 المتصفح لا يدعم الإشعارات');
                    return;
                }

                // التحقق من حالة الإذن الحالية
                if (Notification.permission === 'granted') {
                    console.log('🔕 الإشعارات مفعلة بالفعل');
                    return;
                }

                if (Notification.permission === 'denied') {
                    console.log('🔕 الإشعارات مرفوضة - لن يتم عرض الرسالة');
                    localStorage.setItem('notificationPromptShown', 'true');
                    localStorage.setItem('notificationPermissionDenied', 'true');
                    return;
                }

                // إظهار رسالة التفعيل فقط في الصفحة الرئيسية (dashboard)
                const isDashboardPage = window.location.pathname === '/admin/dashboard' || window.location.pathname === '/admin/';

                if (!isDashboardPage) {
                    console.log('🔕 تم تخطي عرض رسالة تفعيل الإشعارات - ليس في الصفحة الرئيسية');
                    return;
                }

                // التحقق من عدم إظهار الرسالة مسبقاً
                if (localStorage.getItem('notificationPromptShown') === 'true') {
                    console.log('🔕 تم تخطي عرض رسالة تفعيل الإشعارات - تم عرضها مسبقاً');
                    return;
                }

                // التحقق من أن المستخدم لم يرفض الإذن مسبقاً
                if (localStorage.getItem('notificationPermissionDenied') === 'true') {
                    console.log('🔕 تم تخطي عرض رسالة تفعيل الإشعارات - الإذن مرفوض مسبقاً');
                    return;
                }

                const alert = document.getElementById('permissionAlert');
                const enableBtn = document.getElementById('enableNotificationsBtn');

                if (alert) {
                    alert.classList.remove('d-none');
                    alert.style.display = 'block';

                    // تسجيل أن الرسالة تم عرضها
                    localStorage.setItem('notificationPromptShown', 'true');
                    console.log('🔔 تم عرض رسالة تفعيل الإشعارات في الصفحة الرئيسية');
                }
                if (enableBtn) {
                    enableBtn.classList.remove('d-none');
                    enableBtn.style.display = 'inline-block';
                }
            }

            hidePermissionAlert() {
                const alert = document.getElementById('permissionAlert');
                const enableBtn = document.getElementById('enableNotificationsBtn');

                if (alert) {
                    alert.classList.add('d-none');
                    alert.style.display = 'none';
                }
                if (enableBtn) {
                    enableBtn.classList.add('d-none');
                    enableBtn.style.display = 'none';
                }

                // تسجيل أن المستخدم تفاعل مع الرسالة
                localStorage.setItem('notificationPromptShown', 'true');
                console.log('🔕 تم إخفاء رسالة تفعيل الإشعارات');
            }

            async requestNotificationPermission() {
                try {
                    console.log('🔔 طلب إذن الإشعارات...');

                    const permission = await Notification.requestPermission();
                    console.log('📋 نتيجة طلب الإذن:', permission);

                    if (permission === 'granted') {
                        console.log('✅ تم منح إذن الإشعارات');
                        this.hidePermissionAlert();
                        await this.enableNotifications();

                        // إظهار رسالة نجاح
                        this.showSuccessMessage('تم تفعيل الإشعارات بنجاح! ستتلقى الآن جميع التحديثات المهمة.');

                    } else if (permission === 'denied') {
                        console.log('❌ تم رفض إذن الإشعارات');
                        this.updateNotificationStatus('denied');
                        this.hidePermissionAlert();
                        // تسجيل أن المستخدم رفض الإذن
                        localStorage.setItem('notificationPermissionDenied', 'true');
                        alert('تم رفض إذن الإشعارات. يرجى تفعيلها من إعدادات المتصفح.');
                    } else {
                        console.log('⏸️ لم يتم اتخاذ قرار بشأن إذن الإشعارات');
                        this.updateNotificationStatus('default');
                    }
                } catch (error) {
                    console.error('❌ خطأ في طلب إذن الإشعارات:', error);
                    alert('حدث خطأ أثناء طلب إذن الإشعارات: ' + error.message);
                }
            }

            showSuccessMessage(message) {
                // إنشاء toast للنجاح
                const toast = document.createElement('div');
                toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
                toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
                toast.setAttribute('role', 'alert');
                toast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-check-circle me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                `;

                document.body.appendChild(toast);

                // تفعيل Bootstrap toast
                const bsToast = new bootstrap.Toast(toast, {
                    autohide: true,
                    delay: 5000
                });
                bsToast.show();

                // إزالة العنصر بعد الإخفاء
                toast.addEventListener('hidden.bs.toast', () => {
                    document.body.removeChild(toast);
                });
            }

            async loadNotificationsList() {
                const list = document.getElementById('notificationsList');
                if (!list) return;

                // تجنب التحديث المتكرر
                if (this.isUpdating) {
                    console.log('🔄 تحديث الإشعارات جاري بالفعل...');
                    return;
                }

                this.isUpdating = true;

                try {
                    // إظهار حالة التحميل فقط في المرة الأولى
                    if (!this.notificationsLoaded) {
                        list.innerHTML = `
                            <div class="text-center p-3">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                                <div class="mt-2">جاري تحميل الإشعارات...</div>
                            </div>
                        `;
                    }

                    const response = await fetch('/admin/notifications/get');
                    const data = await response.json();

                    if (data.success && data.notifications) {
                        this.notifications = data.notifications;
                        this.notificationCount = data.notifications.filter(n => !n.isRead).length || 0;
                        this.updateNotificationUI(data.notifications);
                        this.notificationsLoaded = true;
                    } else {
                        throw new Error('فشل في تحميل الإشعارات');
                    }
                } catch (error) {
                    console.error('❌ خطأ في تحميل الإشعارات:', error);
                    list.innerHTML = `
                        <div class="text-center p-3 text-muted">
                            <i class="fas fa-exclamation-triangle"></i>
                            <div class="mt-2">فشل في تحميل الإشعارات</div>
                            <button class="btn btn-sm btn-outline-primary mt-2" onclick="window.adminNotifications.loadNotificationsList()">
                                إعادة المحاولة
                            </button>
                        </div>
                    `;
                } finally {
                    this.isUpdating = false;
                }
            }
           getNotificationIcon(type) {
                const icons = {
                    'order': 'shopping-cart',
                    'user': 'user',
                    'system': 'cog',
                    'warning': 'exclamation-triangle',
                    'info': 'info-circle',
                    'success': 'check-circle'
                };
                return icons[type] || 'bell';
            }

            formatTime(dateString) {
                const date = new Date(dateString);
                const now = new Date();
                const diff = now - date;
                const minutes = Math.floor(diff / 60000);
                const hours = Math.floor(minutes / 60);
                const days = Math.floor(hours / 24);

                if (days > 0) return `منذ ${days} يوم`;
                if (hours > 0) return `منذ ${hours} ساعة`;
                if (minutes > 0) return `منذ ${minutes} دقيقة`;
                return 'الآن';
            }

            updateNotificationUI(notifications) {
                const badge = document.getElementById('notificationBadge');
                const statusDot = document.getElementById('notificationStatusDot');
                const sidebarDot = document.getElementById('sidebarNotificationDot');
                const statusText = document.getElementById('notificationStatus');
                const list = document.getElementById('notificationsList');

                // Update badge
                if (badge) {
                    if (this.notificationCount > 0) {
                        badge.textContent = this.notificationCount > 99 ? '99+' : this.notificationCount.toString();
                        badge.classList.remove('d-none');
                    } else {
                        badge.classList.add('d-none');
                    }
                }

                // Update main notification status dot
                if (statusDot) {
                    statusDot.className = 'notification-status-indicator';
                    if (this.isEnabled) {
                        statusDot.classList.add('enabled');
                    } else if (this.isPending) {
                        statusDot.classList.add('pending');
                    }
                }

                // Update sidebar notification status dot
                if (sidebarDot) {
                    sidebarDot.className = 'notification-status-dot';
                    if (this.isEnabled) {
                        sidebarDot.classList.add('enabled');
                    } else if (this.isPending) {
                        sidebarDot.classList.add('pending');
                    }
                }

                // Update dashboard notification count if present
                const dashboardCount = document.getElementById('dashboardNotificationCount');
                if (dashboardCount) {
                    dashboardCount.textContent = this.notificationCount || 0;
                }

                // Update any other notification counters on the page
                const allNotificationCounters = document.querySelectorAll('[data-notification-counter]');
                allNotificationCounters.forEach(counter => {
                    counter.textContent = this.notificationCount || 0;
                });

                // Update status text
                if (statusText) {
                    if (this.isEnabled) {
                        statusText.textContent = 'مفعل';
                    } else if (this.isPending) {
                        statusText.textContent = 'انتظار...';
                    } else {
                        statusText.textContent = 'معطل';
                    }
                }

                // Update notification list
                if (!list) return;

                if (!notifications || notifications.length === 0) {
                    list.innerHTML = `
                        <div class="text-center p-3 text-muted">
                            <i class="fas fa-bell-slash"></i>
                            <div class="mt-2">لا توجد إشعارات</div>
                        </div>
                    `;
                    return;
                }

                const notificationsHTML = notifications.slice(0, 5).map(notification => {
                    let iconType = 'bell'; // افتراضياً أيقونة جرس
                    try {
                        const parsedData = JSON.parse(notification.data);
                        iconType = this.getNotificationIcon(parsedData.type);
                    } catch (e) {
                        console.warn('خطأ في فك JSON:', e);
                    }

                    return `
                    <div class="dropdown-item notification-item ${notification.isRead ? '' : 'unread'}"
                        onclick="markNotificationAsRead(${notification.id})">
                        <div class="d-flex align-items-start">
                            <div class="notification-icon me-3">
                                <i class="fas fa-${iconType}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="notification-title fw-bold">${notification.title || 'بدون عنوان'}</div>
                                <div class="notification-message text-muted small">${notification.message || ''}</div>
                                <div class="notification-time text-muted small">
                                    <i class="fas fa-clock me-1"></i>
                                    ${this.formatTime(notification.createdAt)}
                                </div>
                            </div>
                            ${!notification.isRead ? '<div class="notification-dot"></div>' : ''}
                        </div>
                    </div>
                    `;
                }).join('');

                list.innerHTML = notificationsHTML;
            }

            markAsRead(notificationId) {
                const notification = this.notifications.find(n => n.id === notificationId);
                if (notification && notification.unread) {
                    notification.unread = false;
                    this.notificationCount = Math.max(0, this.notificationCount - 1);
                    this.updateNotificationUI();
                }
            }

            formatTime(date) {
                const now = new Date();
                const d = new Date(date);
                const diff = now - d;
                const minutes = Math.floor(diff / 60000);

                if (minutes < 1) return 'الآن';
                if (minutes < 60) return `منذ ${minutes} دقيقة`;

                const hours = Math.floor(minutes / 60);
                if (hours < 24) return `منذ ${hours} ساعة`;

                const days = Math.floor(hours / 24);
                return `منذ ${days} يوم`;
            }

            showError(message) {
                // Create a temporary alert
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
                alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
                alertDiv.innerHTML = `
                    <i class="fas fa-exclamation-circle"></i> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                document.body.appendChild(alertDiv);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        }

        // دالة تحديد الإشعار كمقروء
        async function markNotificationAsRead(notificationId) {
            try {
                const response = await fetch(`/admin/notifications/${notificationId}/read`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    // إعادة تحميل قائمة الإشعارات
                    if (window.adminNotifications) {
                        window.adminNotifications.loadNotificationsList();
                    }
                }
            } catch (error) {
                console.error('خطأ في تحديد الإشعار كمقروء:', error);
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            // ✅ إصلاح مشكلة toggle-btn غير الموجودة
            const toggleBtn = document.querySelector('.toggle-btn');
            if (toggleBtn) {
                toggleBtn.addEventListener('click', function() {
                    this.nextElementSibling.classList.toggle('show');
                });
            }

            // ✅ سايد بار
            const sidebarToggle = document.getElementById('sidebarToggle');
            const adminSidebar = document.getElementById('adminSidebar');

            if (sidebarToggle && adminSidebar) {
                sidebarToggle.addEventListener('click', () => {
                    adminSidebar.classList.toggle('show');
                });

                document.addEventListener('click', (event) => {
                    if (window.innerWidth <= 1024 &&
                        !adminSidebar.contains(event.target) &&
                        !sidebarToggle.contains(event.target)) {
                        adminSidebar.classList.remove('show');
                    }
                });
            }

            // ✅ تفعيل نظام الإشعارات المدمج AdminNotificationSystem
            try {
                window.adminNotifications = new AdminNotificationSystem();
                console.log("✅ تم تهيئة نظام الإشعارات بنجاح");
            } catch (error) {
                console.error("❌ فشل في تهيئة نظام الإشعارات:", error);
            }

            // ✅ إخفاء أي alerts بعد 5 ثواني
            setTimeout(() => {
                document.querySelectorAll('.alert').forEach(alert => {
                    if (alert.classList.contains('show')) {
                        alert.classList.remove('show');
                        setTimeout(() => alert.remove(), 300);
                    }
                });
            }, 5000);
        });

        // ✅ تنظيف موارد الإشعارات عند الخروج
        window.addEventListener('beforeunload', () => {
            if (window.adminNotifications && window.adminNotifications.updateInterval) {
                clearInterval(window.adminNotifications.updateInterval);
                console.log("🧹 تم تنظيف موارد نظام الإشعارات");
            }
        });
    </script>

</body>
</html>
