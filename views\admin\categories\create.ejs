<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>إضافة فئة جديدة</h1>
        <a href="/admin/categories" class="btn btn-secondary">العودة للفئات</a>
    </div>

    <% if (typeof error !== 'undefined' && error) { %>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <%= error %>
        </div>
    <% } %>

    <div class="card">
        <div class="card-body">
            <form action="/admin/categories" method="POST" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="name" class="form-label">اسم التصنيف</label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">الوصف</label>
                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                </div>

                <div class="mb-3">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status" required>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>

                <!-- حقل رفع الصورة -->
                <div class="mb-3">
                    <label for="image" class="form-label">صورة التصنيف</label>
                    <input type="file" class="form-control" id="image" name="image" accept="image/*">
                </div>

                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">إنشاء التصنيف</button>
                </div>
            </form>
        </div>
    </div>
</div>
