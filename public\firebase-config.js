
// تكوين Firebase محدث - تم إنشاؤه تلقائياً
window.FIREBASE_CONFIG = {
  "apiKey": "AIzaSyBdtHd9YCMgqvBs0KAVaen3Jg4M6x2mdhE",
  "authDomain": "company-firebase-77daf.firebaseapp.com",
  "projectId": "company-firebase-77daf",
  "storageBucket": "company-firebase-77daf.appspot.com",
  "messagingSenderId": "76415949840",
  "appId": "1:76415949840:web:d74b24b187a1abf392fe95",
  "measurementId": "G-YRPEE91G0C"
};
window.VAPID_KEY = 'BBH3EJRa-TWHRVwbD1dmtBbmdfzf3SMi-NA8Zey9jW5LM-m5o5mTNUqTkiKMu6Iwn9dvrhEA69f0ZdL_gPuoJVc';

// دالة مساعدة لتهيئة Firebase
window.initializeFirebaseMessaging = function() {
  if (!firebase || !firebase.messaging) {
    throw new Error('Firebase Messaging غير محمل');
  }
  
  firebase.initializeApp(window.FIREBASE_CONFIG);
  return firebase.messaging();
};

// دالة لحل مشاكل التوكن
window.fixTokenIssues = async function() {
  try {
    const messaging = window.initializeFirebaseMessaging();
    
    // حذف التوكن القديم
    try {
      await messaging.deleteToken();
      console.log('🗑️ تم حذف التوكن القديم');
    } catch (e) {
      console.log('ℹ️ لا يوجد توكن قديم للحذف');
    }
    
    // إعادة تسجيل Service Worker
    const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
      scope: '/',
      updateViaCache: 'none'
    });
    
    await navigator.serviceWorker.ready;
    
    // الحصول على توكن جديد
    const token = await messaging.getToken({
      vapidKey: window.VAPID_KEY,
      serviceWorkerRegistration: registration
    });
    
    return token;
  } catch (error) {
    console.error('خطأ في إصلاح التوكن:', error);
    throw error;
  }
};

console.log('✅ تم تحميل إعدادات Firebase المحدثة');
