
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f5f7fa;
        }

        .product-card {
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            padding: 1rem;
            margin-bottom: 1.5rem;
            transition: transform 0.2s;
            height: 100%;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-image {
            object-fit: cover;
            border-radius: 10px;
        }

        .badge-custom {
            background-color: #8FBC8F;
            color: #fff;
            padding: 0.3rem 0.7rem;
            border-radius: 1rem;
            font-size: 0.8rem;
        }

        .price-tag {
            background-color: #e74c3c;
            color: white;
            padding: 0.3rem 0.7rem;
            border-radius: 1rem;
            font-weight: bold;
            font-size: 0.9rem;
        }
    </style>

<div class="container py-5">
    <h1 class="mb-4 text-center text-success">
        <i class="fas fa-tags me-2"></i> العروض الحالية
    </h1>

    <div class="row">
        <% if (products && products.length > 0) { %>
            <% products.forEach(product => { %>
                <div class="col-md-6 col-lg-4 d-flex">
                    <div class="product-card w-100 d-flex flex-column">
                        <% if (product.image) { %>
                            <img src="<%= product.image %>" class="product-image mb-3" alt="<%= product.name %>">
                        <% } %>
                        <h5><%= product.name %></h5>
                        <p class="text-muted small mb-2"><%= product.description || 'لا يوجد وصف' %></p>
                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            <span class="price-tag"><%= product.price %> ل.س</span>
                            <% if (product.category) { %>
                                <span class="badge-custom"><%= product.category.name %></span>
                            <% } %>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <a href="/admin/products/<%= product.id %>/delete-offer-image" 
                            class="btn btn-sm btn-outline-danger"
                            onclick="return confirm('هل أنت متأكد من حذف صورة العرض؟');">
                                <i class="fas fa-trash-alt"></i> حذف العرض
                            </a>
                        </div>

                    </div>
                </div>
                
            <% }) %>
        <% } else { %>
            <div class="col-12 text-center py-5">
                <i class="fas fa-box fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد عروض حالياً</h4>
            </div>
        <% } %>
    </div>
</div>
