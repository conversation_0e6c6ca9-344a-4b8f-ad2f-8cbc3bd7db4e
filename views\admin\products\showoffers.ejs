
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f5f7fa;
        }

        .product-card {
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            padding: 1rem;
            margin-bottom: 1.5rem;
            transition: transform 0.2s;
            height: 100%;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-image {
            object-fit: cover;
            border-radius: 10px;
        }

        .badge-custom {
            background-color: #8FBC8F;
            color: #fff;
            padding: 0.3rem 0.7rem;
            border-radius: 1rem;
            font-size: 0.8rem;
        }

        .price-tag {
            background-color: #e74c3c;
            color: white;
            padding: 0.3rem 0.7rem;
            border-radius: 1rem;
            font-weight: bold;
            font-size: 0.9rem;
        }
    </style>

<div class="container py-5">
    <h1 class="mb-4 text-center text-success">
        <i class="fas fa-tags me-2"></i> العروض الحالية
    </h1>

    <!-- البحث والفلترة المباشرة -->
    <div class="row mb-4">
        <div class="col-md-4">
            <input type="text" class="form-control" placeholder="البحث في العروض..." id="searchInput">
        </div>
        <div class="col-md-3">
            <select class="form-select" id="categoryFilter">
                <option value="">جميع الفئات</option>
                <!-- سيتم ملؤها بـ JavaScript -->
            </select>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="discountFilter">
                <option value="">جميع الخصومات</option>
                <option value="10-25">10% - 25%</option>
                <option value="25-50">25% - 50%</option>
                <option value="50-75">50% - 75%</option>
                <option value="75-100">75% - 100%</option>
            </select>
        </div>
        <div class="col-md-2">
            <button class="btn btn-secondary w-100" onclick="clearFilters()">
                <i class="fas fa-times"></i> مسح
            </button>
        </div>
    </div>

    <div class="row" id="offersContainer">
        <% if (products && products.length > 0) { %>
            <% products.forEach(product => { %>
                <div class="col-md-6 col-lg-4 d-flex offer-item"
                     data-name="<%= product.name %>"
                     data-description="<%= product.description || '' %>"
                     data-category="<%= product.category ? product.category.name : '' %>"
                     data-price="<%= product.price %>"
                     data-discount="<%= product.discount || 0 %>">
                    <div class="product-card w-100 d-flex flex-column">
                        <% if (product.image) { %>
                            <img src="<%= product.image %>" class="product-image mb-3" alt="<%= product.name %>">
                        <% } %>
                        <h5><%= product.name %></h5>
                        <p class="text-muted small mb-2"><%= product.description || 'لا يوجد وصف' %></p>
                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            <span class="price-tag"><%= product.price %> ل.س</span>
                            <% if (product.category) { %>
                                <span class="badge-custom"><%= product.category.name %></span>
                            <% } %>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <a href="/admin/products/<%= product.id %>/delete-offer-image" 
                            class="btn btn-sm btn-outline-danger"
                            onclick="return confirm('هل أنت متأكد من حذف صورة العرض؟');">
                                <i class="fas fa-trash-alt"></i> حذف العرض
                            </a>
                        </div>

                    </div>
                </div>
                
            <% }) %>
        <% } else { %>
            <div class="col-12 text-center py-5">
                <i class="fas fa-box fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد عروض حالياً</h4>
            </div>
        <% } %>
    </div>
</div>

<script>
    // البحث والفلترة المباشرة للعروض
    document.addEventListener('DOMContentLoaded', function() {
        // ملء قائمة الفئات
        populateCategoryFilter();

        // إضافة مستمعي الأحداث
        document.getElementById('searchInput').addEventListener('input', filterOffers);
        document.getElementById('categoryFilter').addEventListener('change', filterOffers);
        document.getElementById('discountFilter').addEventListener('change', filterOffers);
    });

    function populateCategoryFilter() {
        const offers = document.querySelectorAll('.offer-item');
        const categories = new Set();

        offers.forEach(offer => {
            const category = offer.dataset.category;
            if (category && category.trim() !== '') {
                categories.add(category);
            }
        });

        const categoryFilter = document.getElementById('categoryFilter');
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            categoryFilter.appendChild(option);
        });
    }

    function filterOffers() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const categoryFilter = document.getElementById('categoryFilter').value;
        const discountFilter = document.getElementById('discountFilter').value;

        const offers = document.querySelectorAll('.offer-item');
        let visibleCount = 0;

        offers.forEach(offer => {
            const name = offer.dataset.name.toLowerCase();
            const description = offer.dataset.description.toLowerCase();
            const category = offer.dataset.category;
            const discount = parseFloat(offer.dataset.discount) || 0;

            const matchesSearch = searchTerm === '' ||
                                name.includes(searchTerm) ||
                                description.includes(searchTerm);
            const matchesCategory = !categoryFilter || category === categoryFilter;
            const matchesDiscount = !discountFilter || checkDiscountRange(discount, discountFilter);

            if (matchesSearch && matchesCategory && matchesDiscount) {
                offer.style.display = '';
                visibleCount++;
            } else {
                offer.style.display = 'none';
            }
        });

        // إظهار رسالة عدم وجود نتائج
        updateNoResultsMessage(visibleCount);
    }

    function checkDiscountRange(discount, range) {
        const [min, max] = range.split('-').map(Number);
        return discount >= min && discount <= max;
    }

    function updateNoResultsMessage(visibleCount) {
        let noResultsDiv = document.getElementById('noResultsDiv');

        if (visibleCount === 0) {
            if (!noResultsDiv) {
                const container = document.getElementById('offersContainer');
                noResultsDiv = document.createElement('div');
                noResultsDiv.id = 'noResultsDiv';
                noResultsDiv.className = 'col-12 text-center py-5';
                noResultsDiv.innerHTML = `
                    <i class="fas fa-search fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد عروض مطابقة لمعايير البحث</h4>
                `;
                container.appendChild(noResultsDiv);
            }
            noResultsDiv.style.display = '';
        } else {
            if (noResultsDiv) {
                noResultsDiv.style.display = 'none';
            }
        }
    }

    function clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('categoryFilter').value = '';
        document.getElementById('discountFilter').value = '';
        filterOffers();
    }
</script>
