const { Admin, Customer, Category, Order, Product, DeliveryPerson, AdminToken, Notification } = require('../models');

class AdminController {
  // Admin Dashboard
  async dashboard(req, res) {
    try {
      const [customers, admin, categories, orders,orderspending, products, deliveryPeople,notifications] = await Promise.all([
        Customer.count(),
        Admin.findByPk(req.user.id),
        Category.count(),
        Order.count(),
        Order.count({
          where: {
            status: 'pending'
          }
        }),
        Product.count(),
        DeliveryPerson.count(),
        Notification.count({
          where: {
            adminId: req.user.id,
            readAt: null
          }
        })
      ]);

      res.render('admin/dashboard', {
        customers,
        admin,
        categories,
        orders,
        orderspending,
        products,
        deliveryPeople,
        notifications
      });
    } catch (error) {
      res.status(500).render('error', { error });
    }
  }

async AdminToken(req, res) {
  const { token } = req.body;
  const adminId = req.session?.adminId || req.admin?.id;

  if (!token || !adminId) {
    return res.status(400).json({ success: false, message: 'توكن أو معرف الأدمن مفقود' });
  }

  try {
    const existingToken = await AdminToken.findOne({ where: { token } });

    if (existingToken) {
      // حدث التوكن الحالي
      await existingToken.update({
        adminId,
        updatedAt: new Date()
      });
    } else {
      // أنشئ توكن جديد
      await AdminToken.create({
        token,
        adminId,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }

    res.json({ success: true, message: 'تم حفظ توكن الإشعارات بنجاح' });

  } catch (err) {
    console.error('❌ خطأ في حفظ التوكن:', err);
    res.status(500).json({ success: false, message: 'خطأ في حفظ التوكن' });
  }
}

}

module.exports = new AdminController();
