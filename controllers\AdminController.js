const { Admin, Customer, Category, Order, Product, DeliveryPerson, AdminToken, Notification,OrderDetail } = require('../models');
const { Op } = require('sequelize');

class AdminController {
  // Admin Dashboard
  async dashboard(req, res) {
    try {
      const [customers, admin, categories, orders,orderspending, products, deliveryPeople,notifications] = await Promise.all([
        Customer.count(),
        Admin.findByPk(req.user.id),
        Category.count(),
        Order.count(),
        Order.count({
          where: {
            status: 'pending'
          }
        }),
        Product.count(),
        DeliveryPerson.count(),
        Notification.count({
          where: {
            adminId: req.user.id,
            readAt: null
          }
        })
      ]);

         const todayOrders = await Order.findAll({
            where: {
                createdAt: {
                    [Op.gte]: new Date(new Date().setHours(0, 0, 0, 0))
                }
            }
          });
          const totalValue = todayOrders.reduce((sum, order) => sum + parseFloat(order.totalPrice || 0), 0);

          const stats = {
              totalorders: todayOrders.length,
              todayPending: await Order.count({
                  where: {
                      status: 'pending',
                      createdAt: {
                          [Op.gte]: new Date(new Date().setHours(0, 0, 0, 0))
                      }
                  }
              }),
              totalValue,
              todaycustomers: await Customer.count({
                  where: {
                      createdAt: {
                          [Op.gte]: new Date(new Date().setHours(0, 0, 0, 0))
                      }
                  }
              })
          };


      res.render('admin/dashboard', {
        stats,
        customers,
        admin,
        categories,
        orders,
        orderspending,
        products,
        deliveryPeople,
        notifications
      });
    } catch (error) {
      res.status(500).render('error', { error });
    }
  }
}

module.exports = new AdminController();
