'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class DeliveryPeopleArea extends Model {
    static associate(models) {
      DeliveryPeopleArea.belongsTo(models.DeliveryPerson, {
        foreignKey: 'deliveryPeople_id',
        as: 'deliveryPerson'
      });
    }
  }
  
  DeliveryPeopleArea.init({
    deliveryPeople_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'DeliveryPeople',
        key: 'id'
      }
    },
    address: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'عنوان السائق'
    },
    city: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'المدينة'
    },
    region: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'المنطقة'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'DeliveryPeopleArea',
    tableName: 'DeliveryPeopleAreas',
    indexes: [
      {
        unique: true,
        fields: ['deliveryPeople_id']
      }
    ]
  });

  return DeliveryPeopleArea;
};
