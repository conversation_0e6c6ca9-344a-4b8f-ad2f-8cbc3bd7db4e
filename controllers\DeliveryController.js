const { Order, Delivery, Customer, Delivery<PERSON>erson, DeliveryPeopleArea, OrderDetail, Product } = require('../models');
const NotificationsFunction = require('../controllers/NotificationsFunction');
const { Op } = require('sequelize');

class DeliveryController {

  // عرض توصيلات سائق معين 
  async personDeliveries(req, res) {
    const { personId } = req.params;

    try {
      const inProgressDeliveries = await Delivery.findAll({
        where: {
          deliveryPersonId: personId,
          status: ['pending', 'in_transit']
        },
        include: [
          {
            model: Order,
            as: 'order',
            include: ['customer']
          },
          { model: DeliveryPerson, as: 'courier' }
        ],
        order: [['createdAt', 'DESC']]
      });

      const completedDeliveries = await Delivery.findAll({
        where: {
          deliveryPersonId: personId,
          status: ['delivered', 'cancelled']
        },
        include: [
          {
            model: Order,
            as: 'order',
            include: ['customer']
          },
          { model: DeliveryPerson, as: 'courier' }
        ],
        order: [['createdAt', 'DESC']]
      });

      const person = await DeliveryPerson.findByPk(personId, { attributes: ['name'] });
      const deliveryPersonName = person ? person.name : null;

      res.render('admin/delivery/index', {
        personId,
        deliveryPersonName,
        inProgressDeliveries,
        completedDeliveries
      });

    } catch (error) {
      console.error(error);
      res.status(500).render('error', { error: { message: 'Server error' } });
    }
  }
  
  // عرض كل التوصيلات الخاصة بالمتجر (dashboard)
  async index(req, res) {
    try {
      const currentPage = parseInt(req.query.page) || 1;
      const limit = 20;
      const offset = (currentPage - 1) * limit;
  
      const { count, rows: deliveries } = await Delivery.findAndCountAll({
        include: [
          {
            model: Order,
            as: 'order',
            include: [{ model: Customer, as: 'customer' }]
          },
          { model: DeliveryPerson, as: 'courier' }
        ],
        order: [['createdAt', 'DESC']],
        limit,
        offset
      });
  
      const totalPages = Math.ceil(count / limit);
  
      res.render('admin/delivery/dashboard', {
        deliveries,
        currentPage,
        totalPages
      });
  
    } catch (error) {
      console.error('Error fetching deliveries:', error);
      res.status(500).render('error', { error: { message: 'Unable to fetch deliveries' } });
    }
  }
  
  // عرض صفحة تفاصيل التوصيل لطلب معين
  async show(req, res) {
    try {
        const orderId = req.params.id;
        const order = await Order.findByPk(orderId, {
          include: [
            { model: Delivery, as: 'delivery',
              include: [{ model: DeliveryPerson, as: 'courier' }]
            },
            { model: Customer, as: 'customer' },
            
          ]
        });
        if (!order) return res.status(404).render('error', { error: { message: 'Order not found' } });

        res.render('admin/delivery/show', { order, delivery: order.delivery });
        } catch (error) {
          console.error(error);
          res.status(500).render('error', { error: { message: 'Unable to fetch delivery details' } });
        }
  }

 // عرض نموذج إنشاء توصيل جديد (مع اختيار سائق تلقائي)
  async createForm(req, res) {
    const deliveryPersonId = req.query.deliveryPersonId || null;
    const orderId = req.query.orderId;
    const showAll = req.query.showAll === 'on';

    try {
        const pendingOrders = await Order.findAll({
        where: {
          status: {
            [Op.in]: ['pending', 'processing']
          }
        },
        include: [{ model: Customer, as: 'customer' }]
      });

      let deliveryPeople = [];

      if (showAll) {
        // عرض جميع السائقين النشطين
        deliveryPeople = await DeliveryPerson.findAll({
          where: { status: 'active' },
          include: [{ model: DeliveryPeopleArea, as: 'areas' }]
        });
      } else if (pendingOrders.length > 0) {
        // عرض فقط السائقين في نفس مدينة الزبون
        const order = pendingOrders[0];

        deliveryPeople = await DeliveryPerson.findAll({
          where: { status: 'active' },
          include: [{
            model: DeliveryPeopleArea,
            as: 'areas',
            where: {
              city: order.customer.city
            }
          }]
        });
      }

      res.render('admin/delivery/create', {
        deliveryPeople,
        pendingOrders,
        selectedDeliveryPersonId: deliveryPersonId,
        showAll,
        errors: null,
        formData: {orderId}
      });

    } catch (error) {
      console.error(error);
      res.status(500).send('Server error');
    }
  }

  // معالجة إنشاء توصيل جديد من نموذج
  async create(req, res) {
    const { deliveryPersonId, orderId, status, pickupTime, deliveryTime, notes } = req.body;

    try {
    const existingDelivery = await Delivery.findOne({ where: { orderId } });
    if (existingDelivery) {
      // إذا موجود، ارجع رسالة خطأ أو إعادة توجيه مع رسالة
      return res.status(400).render('error', {
        error: 'هذا الطلب لديه توصيل موجود مسبقاً.',
        orderId
      });
    }
      const delivery = await Delivery.create({
        orderId: orderId || null,
        deliveryPersonId: deliveryPersonId || null,
        status,
        pickupTime: pickupTime || null,
        deliveryTime: deliveryTime || null,
        notes: notes || null,
      });
      await Order.update({ status: 'out_for_delivery' }, { where: { id: orderId } });

      NotificationsFunction.sendNewDeliveryNotifications(delivery);
    

      res.redirect(`/admin/deliveries`);
    } catch (error) {
      console.error(error);
      const deliveryPeople = await DeliveryPerson.findAll();
      res.render('admin/deliveries/create', {
        deliveryPeople,
        selectedDeliveryPersonId: deliveryPersonId,
        errors: error.errors,
        formData: req.body
      });
    }
  }

  // عرض نموذج تعديل توصيل
async editForm(req, res) {
  const deliveryId = req.params.id;
  const showAll = req.query.showAll === 'on';

  try {
    // التوصيل الحالي
    const delivery = await Delivery.findByPk(deliveryId, {
      include: [
        { model: Order, as: 'order', include: [{ model: Customer, as: 'customer' }] }
      ]
    });

    if (!delivery) {
      return res.status(404).send('Delivery not found');
    }

    // نفس pending orders
    const pendingOrders = await Order.findAll({
      where: {
        status: {
          [Op.in]: ['pending', 'processing']
        }
      },
      include: [{ model: Customer, as: 'customer' }]
    });

    let deliveryPeople = [];

    if (showAll) {
      deliveryPeople = await DeliveryPerson.findAll({
        where: { status: 'active' },
        include: [{ model: DeliveryPeopleArea, as: 'areas' }]
      });
    } else if (pendingOrders.length > 0) {
      const order = pendingOrders[0];
      deliveryPeople = await DeliveryPerson.findAll({
        where: { status: 'active' },
        include: [{
          model: DeliveryPeopleArea,
          as: 'areas',
          where: {
            city: order.customer.city
          }
        }]
      });
    }

    res.render('admin/delivery/edit', {
      delivery,
      deliveryPeople,
      pendingOrders,
      showAll,
      errors: null
    });

  } catch (error) {
    console.error(error);
    res.status(500).send('Server error');
  }
}


  // تحديث بيانات توصيل
  async update(req, res) {
    try {
    const {deliveryPersonId, status, pickupTime, deliveryTime, notes } = req.body;
    const delivery = await Delivery.findByPk(req.params.id);
    if (!delivery) return res.status(404).render('error', { error: { message: 'Delivery not found' } });

    await delivery.update({ deliveryPersonId,status, pickupTime, deliveryTime, notes });
    res.redirect('/admin/deliveries');
    } catch (error) {
      console.error(error);
      res.status(500).render('error', { error: { message: 'Unable to update delivery' } });
    }
  }

  // حذف توصيل
  async delete(req, res) {
    const delivery = await Delivery.findByPk(req.params.id);
    if (!delivery) return res.status(404).render('error', { error: { message: 'Delivery not found' } });

    await delivery.destroy();
    res.redirect('/admin/drivers/'+req.params.id+'/deliveries');
  }

  // تحديث حالة التوصيل (مثلاً من صفحة خاصة)
  async markComplete(req, res) {
    const { deliveryId } = req.params;
    try {
      // جلب التوصيل مع الطلب المرتبط للتأكد من صلاحية المتجر
      const delivery = await Delivery.findOne({
        where: { id: deliveryId },
        include: {
          model: Order,
          as: 'order',
        }
      });
  
      if (!delivery) {
        return res.status(404).render('error', { error: { message: 'Delivery not found' } });
      }
  
      // تحديث حالة التوصيل
      delivery.status = 'delivered'; // أو حسب الحالة التي تستخدمها
      await delivery.save();
  
      // تحديث حالة الطلب المرتبط
     /* delivery.order.status = 'delivered'; // حسب النظام لديك
      await delivery.order.save();*/

      res.redirect('/admin/drivers'+deliveryId+'/deliveries');
    } catch (error) {
      console.error(error);
      res.status(500).render('error', { error: { message: 'Unable to update delivery status' } });
    }
  }

  async showDelivery(req, res) {
    const { orderId } = req.params;
  
    try {
      // جلب التوصيل المرتبط بالطلب مع بيانات السائق والطلب والعميل (حسب الربط)
      const delivery = await Delivery.findOne({
        where: { orderId },
        include: [
          {
            model: Order,
            as: 'order',
            include: [{ model: Customer, as: 'customer' }]
          },
          {
            model: DeliveryPerson, // اسم الموديل للسائق عندك
            as: 'courier'          // تأكد من alias في associations
          }
        ]
      });
  
      if (!delivery) {
        return res.status(404).render('error', { error: { message: 'Delivery not found' } });
      }
  
      res.render('admin/delivery/show', { delivery });
    } catch (error) {
      console.error(error);
      res.status(500).render('error', { error: { message: 'Unable to fetch delivery details' } });
    }
  }
  
}

module.exports = new DeliveryController();
