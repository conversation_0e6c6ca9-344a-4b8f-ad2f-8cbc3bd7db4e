#!/usr/bin/env node

/**
 * Firebase Notifications Diagnostic Script
 * يساعد في تشخيص مشاكل الإشعارات في Firebase
 */

require('dotenv').config();
const admin = require('firebase-admin');
const { AdminToken } = require('../models');
const { serviceAccountPath, firebaseConfig } = require('../config/firebase');
const FirebaseMessagingService = require('../services/FirebaseMessagingService');

// تهيئة Firebase Admin SDK
try {
  const serviceAccount = require(`../${serviceAccountPath}`);
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
  console.log('✅ Firebase Admin SDK initialized');
} catch (error) {
  console.error('❌ Failed to initialize Firebase Admin SDK:', error);
  process.exit(1);
}

class FirebaseDiagnostics {
  
  /**
   * فحص شامل لإعدادات Firebase
   */
  static async runFullDiagnostics() {
    console.log('\n🔍 بدء الفحص الشامل لإعدادات Firebase...\n');
    
    try {
      await this.checkFirebaseConfig();
      await this.checkServiceAccount();
      await this.checkAdminTokens();
      await this.testNotificationSending();
      await this.checkVAPIDKey();
      
      console.log('\n✅ انتهى الفحص الشامل');
    } catch (error) {
      console.error('\n❌ فشل الفحص الشامل:', error);
    }
  }

  /**
   * فحص إعدادات Firebase
   */
  static async checkFirebaseConfig() {
    console.log('📋 فحص إعدادات Firebase...');
    
    const requiredFields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
    const missingFields = requiredFields.filter(field => !firebaseConfig[field]);
    
    if (missingFields.length > 0) {
      console.error(`❌ حقول مفقودة في إعدادات Firebase: ${missingFields.join(', ')}`);
      return false;
    }
    
    console.log('✅ إعدادات Firebase صحيحة');
    console.log(`   - Project ID: ${firebaseConfig.projectId}`);
    console.log(`   - Storage Bucket: ${firebaseConfig.storageBucket}`);
    console.log(`   - Messaging Sender ID: ${firebaseConfig.messagingSenderId}`);
    
    return true;
  }

  /**
   * فحص Service Account
   */
  static async checkServiceAccount() {
    console.log('\n🔑 فحص Service Account...');
    
    try {
      const serviceAccount = require(`../${serviceAccountPath}`);
      
      const requiredFields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email'];
      const missingFields = requiredFields.filter(field => !serviceAccount[field]);
      
      if (missingFields.length > 0) {
        console.error(`❌ حقول مفقودة في Service Account: ${missingFields.join(', ')}`);
        return false;
      }
      
      console.log('✅ Service Account صحيح');
      console.log(`   - Project ID: ${serviceAccount.project_id}`);
      console.log(`   - Client Email: ${serviceAccount.client_email}`);
      
      return true;
    } catch (error) {
      console.error('❌ فشل في قراءة Service Account:', error.message);
      return false;
    }
  }

  /**
   * فحص توكنات المديرين
   */
  static async checkAdminTokens() {
    console.log('\n📱 فحص توكنات المديرين...');
    
    try {
      const adminTokens = await AdminToken.findAll();
      
      if (adminTokens.length === 0) {
        console.warn('⚠️ لا توجد توكنات مديرين مسجلة');
        return false;
      }
      
      console.log(`📊 عدد التوكنات المسجلة: ${adminTokens.length}`);
      
      // فحص صحة التوكنات
      let validTokens = 0;
      let invalidTokens = 0;
      
      for (const adminToken of adminTokens) {
        const isValid = await FirebaseMessagingService.validateToken(adminToken.token);
        if (isValid) {
          validTokens++;
          console.log(`✅ توكن صالح: ${adminToken.token.substring(0, 20)}...`);
        } else {
          invalidTokens++;
          console.log(`❌ توكن غير صالح: ${adminToken.token.substring(0, 20)}...`);
        }
      }
      
      console.log(`📈 النتائج: ${validTokens} صالح، ${invalidTokens} غير صالح`);
      
      return validTokens > 0;
    } catch (error) {
      console.error('❌ فشل في فحص توكنات المديرين:', error.message);
      return false;
    }
  }

  /**
   * اختبار إرسال الإشعارات
   */
  static async testNotificationSending() {
    console.log('\n🧪 اختبار إرسال الإشعارات...');
    
    try {
      const adminTokens = await AdminToken.findAll();
      
      if (adminTokens.length === 0) {
        console.warn('⚠️ لا توجد توكنات لاختبار الإرسال');
        return false;
      }
      
      const testNotification = {
        title: 'اختبار الإشعارات',
        body: 'هذا إشعار تجريبي للتأكد من عمل النظام'
      };
      
      const testData = {
        type: 'test',
        timestamp: Date.now().toString()
      };
      
      const result = await FirebaseMessagingService.sendToAllAdmins(testNotification, testData);
      
      console.log(`📊 نتائج الاختبار:`);
      console.log(`   - نجح: ${result.successCount}`);
      console.log(`   - فشل: ${result.failureCount}`);
      
      return result.successCount > 0;
    } catch (error) {
      console.error('❌ فشل اختبار الإرسال:', error.message);
      return false;
    }
  }

  /**
   * فحص VAPID Key
   */
  static async checkVAPIDKey() {
    console.log('\n🔐 فحص VAPID Key...');
    
    const { vapidKey } = require('../config/firebase');
    
    if (!vapidKey) {
      console.error('❌ VAPID Key غير موجود');
      return false;
    }
    
    if (vapidKey.length < 80) {
      console.error('❌ VAPID Key قصير جداً (يجب أن يكون 87 حرف على الأقل)');
      return false;
    }
    
    console.log('✅ VAPID Key موجود وبالطول الصحيح');
    console.log(`   - الطول: ${vapidKey.length} حرف`);
    console.log(`   - البداية: ${vapidKey.substring(0, 20)}...`);
    
    return true;
  }

  /**
   * تنظيف التوكنات غير الصالحة
   */
  static async cleanupInvalidTokens() {
    console.log('\n🧹 تنظيف التوكنات غير الصالحة...');
    
    try {
      const removedCount = await FirebaseMessagingService.cleanupInvalidTokens();
      console.log(`✅ تم حذف ${removedCount} توكن غير صالح`);
      return true;
    } catch (error) {
      console.error('❌ فشل في تنظيف التوكنات:', error.message);
      return false;
    }
  }

  /**
   * عرض معلومات النظام
   */
  static async showSystemInfo() {
    console.log('\n💻 معلومات النظام:');
    console.log(`   - Node.js: ${process.version}`);
    console.log(`   - Platform: ${process.platform}`);
    console.log(`   - Firebase Admin SDK: ${require('firebase-admin/package.json').version}`);
    console.log(`   - Environment: ${process.env.NODE_ENV || 'development'}`);
  }
}

// تشغيل الفحص إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  const command = process.argv[2];
  
  switch (command) {
    case 'full':
      FirebaseDiagnostics.runFullDiagnostics();
      break;
    case 'tokens':
      FirebaseDiagnostics.checkAdminTokens();
      break;
    case 'test':
      FirebaseDiagnostics.testNotificationSending();
      break;
    case 'cleanup':
      FirebaseDiagnostics.cleanupInvalidTokens();
      break;
    case 'info':
      FirebaseDiagnostics.showSystemInfo();
      break;
    default:
      console.log('استخدام:');
      console.log('  node scripts/firebase-diagnostics.js full     - فحص شامل');
      console.log('  node scripts/firebase-diagnostics.js tokens   - فحص التوكنات');
      console.log('  node scripts/firebase-diagnostics.js test     - اختبار الإرسال');
      console.log('  node scripts/firebase-diagnostics.js cleanup  - تنظيف التوكنات');
      console.log('  node scripts/firebase-diagnostics.js info     - معلومات النظام');
  }
}

module.exports = FirebaseDiagnostics;
