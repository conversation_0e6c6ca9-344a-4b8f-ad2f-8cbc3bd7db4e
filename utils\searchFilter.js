const { Op } = require('sequelize');

/**
 * بناء شروط البحث والفلتر للجداول
 * @param {Object} query - معاملات البحث من الـ request
 * @param {Object} searchFields - الحقول القابلة للبحث
 * @param {Object} filterFields - الحقول القابلة للفلتر
 * @returns {Object} شروط البحث والفلتر
 */
function buildSearchAndFilter(query, searchFields = {}, filterFields = {}, fixedFilters = {}) {
    const searchConditions = {};
    const filterConditions = { ...fixedFilters };
    const { search, ...filters } = query;

    // شروط البحث
    if (search && search.trim()) {
        const searchClauses = [];

        // نصية
        if (searchFields.text && searchFields.text.length > 0) {
            searchFields.text.forEach(field => {
                searchClauses.push({
                    [field]: {
                        [Op.like]: `%${search.trim()}%`
                    }
                });
            });
        }

        // رقمية
        if (searchFields.numeric && searchFields.numeric.length > 0) {
            const numericSearch = parseFloat(search.trim());
            if (!isNaN(numericSearch)) {
                searchFields.numeric.forEach(field => {
                    searchClauses.push({
                        [field]: numericSearch
                    });
                });
            }
        }

        if (searchClauses.length > 0) {
            searchConditions[Op.or] = searchClauses;
        }
    }

    // شروط الفلترة
    Object.keys(filters).forEach(key => {
        if (filters[key] && filters[key] !== '' && filterFields[key]) {
            const filterConfig = filterFields[key];

            switch (filterConfig.type) {
                case 'exact':
                    filterConditions[key] = filters[key];
                    break;

                case 'like':
                    filterConditions[key] = {
                        [Op.like]: `%${filters[key]}%`
                    };
                    break;

                case 'in':
                    const values = Array.isArray(filters[key]) ? filters[key] : [filters[key]];
                    filterConditions[key] = {
                        [Op.in]: values
                    };
                    break;

                case 'range':
                    if (filterConfig.min && filters[key + '_min']) {
                        filterConditions[key] = filterConditions[key] || {};
                        filterConditions[key][Op.gte] = filters[key + '_min'];
                    }
                    if (filterConfig.max && filters[key + '_max']) {
                        filterConditions[key] = filterConditions[key] || {};
                        filterConditions[key][Op.lte] = filters[key + '_max'];
                    }
                    break;

                case 'date':
                    if (filters[key + '_from']) {
                        filterConditions[key] = filterConditions[key] || {};
                        filterConditions[key][Op.gte] = new Date(filters[key + '_from']);
                    }
                    if (filters[key + '_to']) {
                        filterConditions[key] = filterConditions[key] || {};
                        filterConditions[key][Op.lte] = new Date(filters[key + '_to'] + ' 23:59:59');
                    }
                    break;

                default:
                    filterConditions[key] = filters[key];
            }
        }
    });

    return {
        searchConditions,
        filterConditions
    };
}


/**
 * بناء خيارات الترتيب
 * @param {string} sortBy - الحقل المراد الترتيب حسبه
 * @param {string} sortOrder - اتجاه الترتيب (asc/desc)
 * @param {string} defaultSort - الترتيب الافتراضي
 * @returns {Array} خيارات الترتيب
 */
function buildSortOptions(sortBy, sortOrder = 'desc', defaultSort = 'createdAt') {
    const validSortOrders = ['asc', 'desc'];
    const order = validSortOrders.includes(sortOrder?.toLowerCase()) ? sortOrder.toLowerCase() : 'desc';
    const field = sortBy || defaultSort;
    
    return [[field, order.toUpperCase()]];
}

/**
 * بناء خيارات الـ pagination
 * @param {number} page - رقم الصفحة
 * @param {number} limit - عدد العناصر في الصفحة
 * @returns {Object} خيارات الـ pagination
 */
function buildPaginationOptions(page = 1, limit = 20) {
    const pageNum = Math.max(1, parseInt(page) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(limit) || 20));
    const offset = (pageNum - 1) * limitNum;
    
    return {
        page: pageNum,
        limit: limitNum,
        offset
    };
}

/**
 * حساب معلومات الـ pagination
 * @param {number} totalCount - العدد الإجمالي للعناصر
 * @param {number} page - رقم الصفحة الحالية
 * @param {number} limit - عدد العناصر في الصفحة
 * @returns {Object} معلومات الـ pagination
 */
function calculatePaginationInfo(totalCount, page, limit) {
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;
    
    return {
        totalCount,
        totalPages,
        currentPage: page,
        hasNextPage,
        hasPrevPage,
        nextPage: hasNextPage ? page + 1 : null,
        prevPage: hasPrevPage ? page - 1 : null,
        startItem: totalCount > 0 ? ((page - 1) * limit) + 1 : 0,
        endItem: Math.min(page * limit, totalCount)
    };
}

/**
 * تنظيف معاملات البحث والفلتر للعرض
 * @param {Object} query - معاملات البحث
 * @returns {Object} معاملات منظفة
 */
function sanitizeFilters(query) {
    const sanitized = {};
    
    Object.keys(query).forEach(key => {
        if (query[key] !== undefined && query[key] !== null && query[key] !== '') {
            sanitized[key] = query[key];
        }
    });
    
    return sanitized;
}

module.exports = {
    buildSearchAndFilter,
    buildSortOptions,
    buildPaginationOptions,
    calculatePaginationInfo,
    sanitizeFilters
};
