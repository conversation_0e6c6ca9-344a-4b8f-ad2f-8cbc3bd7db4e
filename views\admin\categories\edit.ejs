<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>تعديل الفئة: <%= category.name %></h1>
        <a href="/admin/categories" class="btn btn-secondary">العودة للفئات</a>
    </div>

    <% if (typeof error !== 'undefined' && error) { %>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <%= error %>
        </div>
    <% } %>

    <div class="card">
        <div class="card-body">
            <form action="/admin/categories/<%= category.id %>" method="POST" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="name" class="form-label">اسم التصنيف</label>
                    <input type="text" class="form-control" id="name" name="name" 
                           value="<%= category.name %>" required>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">الوصف</label>
                    <textarea class="form-control" id="description" name="description" rows="3"><%= category.description || '' %></textarea>
                </div>

                <div class="mb-3">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status" required>
                        <option value="active" <%= category.status === 'active' ? 'selected' : '' %>>نشط</option>
                        <option value="inactive" <%= category.status === 'inactive' ? 'selected' : '' %>>غير نشط</option>
                    </select>
                </div>

                <% if (category.image) { %>
                    <div class="mb-3">
                        <label class="form-label">الصورة الحالية</label>
                        <div class="current-image-container">
                            <img src="<%= category.image %>" alt="<%= category.name %>" 
                                 class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                        </div>
                    </div>
                <% } %>

                <div class="mb-3">
                    <label for="image" class="form-label">صورة جديدة</label>
                    <input type="file" class="form-control" id="image" name="image" accept="image/*">
                    <small class="form-text text-muted">اختر صورة جديدة إذا كنت تريد استبدال الصورة الحالية (حد أقصى 2MB)</small>
                </div>

                <div class="d-grid d-md-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                    </button>
                    <a href="/admin/categories" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    .current-image-container {
        padding: 10px;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        background: #f8f9fa;
        display: inline-block;
    }

    .form-text {
        color: #6c757d;
    }
</style>
