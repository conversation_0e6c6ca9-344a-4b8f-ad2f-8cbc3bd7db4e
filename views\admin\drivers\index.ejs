<div class="container mt-4">
  <h3 class="mb-3">مندوبي التوصيل</h3>
  <div class="d-flex justify-content-between mb-3">
    <a href="/admin/drivers/create" class="btn btn-primary">إضافة مندوب توصيل</a>
  </div>

  <!-- البحث والفلترة المباشرة -->
  <div class="row mb-4">
    <div class="col-md-6">
      <input type="text" class="form-control" placeholder="البحث في مندوبي التوصيل..." id="searchInput">
    </div>
    <div class="col-md-4">
      <select class="form-select" id="statusFilter">
        <option value="">جميع الحالات</option>
        <option value="active">نشط</option>
        <option value="inactive">غير نشط</option>
      </select>
    </div>
    <div class="col-md-2">
      <button class="btn btn-secondary w-100" onclick="clearFilters()">
        <i class="fas fa-times"></i> مسح
      </button>
    </div>
  </div>

  <div class="card">
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-bordered text-center align-middle">
          <thead class="table-light">
            <tr>
              <th>الصورة</th>
              <th>الاسم</th>
              <th>رقم الهاتف</th>
              <th>عدد التوصيلات</th>
              <th>الحالة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            <% if (deliveryPeople.length > 0) { %>
              <% deliveryPeople.forEach(person => { %>
                <tr class="driver-item"
                    data-name="<%= person.name %>"
                    data-phone="<%= person.phoneNumber %>"
                    data-status="<%= person.status %>"
                    data-deliveries="<%= person.deliveries ? person.deliveries.length : 0 %>">
                  <td>
                    <% if (person.image) { %>
                      <img src="<%= person.image %>" alt="صورة" class="rounded-circle" width="50" height="50">
                    <% } else { %>
                      <span class="text-muted">لا توجد صورة</span>
                    <% } %>
                  </td>
                  <td><a href="/admin/drivers/<%= person.id %>/deliveries"><%= person.name %></a></td>
                  <td><%= person.phoneNumber %></td>
                  <td>
                    <a href="/admin/drivers/<%= person.id %>/deliveries">
                      <%= person.deliveries ? person.deliveries.length : 0 %>
                    </a>
                  </td>
                  <td>
                    <% if (person.status === 'active') { %>
                      <span class="badge bg-success">نشط</span>
                    <% } else { %>
                      <span class="badge bg-secondary">غير نشط</span>
                    <% } %>
                  </td>
                  <td>
                    <a href="/admin/deliveries/create?deliveryPersonId=<%= person.id %>" class="btn btn-success btn-sm ms-1">
                      إضافة توصيل
                    </a>
                    <a href="/admin/drivers/<%= person.id %>/edit" class="btn btn-warning btn-sm">تعديل</a>
                    <form action="/admin/drivers/<%= person.id %>/delete" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا المندوب؟')">
                      <button type="submit" class="btn btn-danger btn-sm">حذف</button>
                    </form>
                  </td>
                </tr>
              <% }) %>
            <% } else { %>
              <tr>
                <td colspan="6" class="text-muted">لا يوجد مندوبي توصيل</td>
              </tr>
            <% } %>
          </tbody>
        </table>
      </div>
    </div>
  </div>

 <% if (totalPages > 1) { %>
  <nav class="d-flex justify-content-center mt-4">
    <ul class="pagination">
      <% if (currentPage > 1) { %>
        <li class="page-item">
          <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
        </li>
      <% } else { %>
        <li class="page-item disabled">
          <span class="page-link">السابق</span>
        </li>
      <% } %>

      <% for (let i = 1; i <= totalPages; i++) { %>
        <li class="page-item <%= currentPage === i ? 'active' : '' %>">
          <a class="page-link" href="?page=<%= i %>"><%= i %></a>
        </li>
      <% } %>

      <% if (currentPage < totalPages) { %>
        <li class="page-item">
          <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
        </li>
      <% } else { %>
        <li class="page-item disabled">
          <span class="page-link">التالي</span>
        </li>
      <% } %>
    </ul>
  </nav>
<% } %>
</div>

<script>
  // البحث والفلترة المباشرة لمندوبي التوصيل
  document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('searchInput').addEventListener('input', filterDrivers);
    document.getElementById('statusFilter').addEventListener('change', filterDrivers);
  });

  function filterDrivers() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;

    const drivers = document.querySelectorAll('.driver-item');
    let visibleCount = 0;

    drivers.forEach(driver => {
      const name = driver.dataset.name.toLowerCase();
      const phone = driver.dataset.phone.toLowerCase();
      const status = driver.dataset.status;

      const matchesSearch = searchTerm === '' ||
                          name.includes(searchTerm) ||
                          phone.includes(searchTerm);
      const matchesStatus = !statusFilter || status === statusFilter;

      if (matchesSearch && matchesStatus) {
        driver.style.display = '';
        visibleCount++;
      } else {
        driver.style.display = 'none';
      }
    });

    // إظهار رسالة عدم وجود نتائج
    updateNoResultsMessage(visibleCount);
  }

  function updateNoResultsMessage(visibleCount) {
    let noResultsRow = document.getElementById('noResultsRow');

    if (visibleCount === 0) {
      if (!noResultsRow) {
        const tbody = document.querySelector('tbody');
        noResultsRow = document.createElement('tr');
        noResultsRow.id = 'noResultsRow';
        noResultsRow.innerHTML = `
          <td colspan="6" class="text-center py-4">
            <div class="text-muted">
              <i class="fas fa-search fa-3x mb-3"></i>
              <p class="mb-0">لا توجد مندوبين مطابقين لمعايير البحث</p>
            </div>
          </td>
        `;
        tbody.appendChild(noResultsRow);
      }
      noResultsRow.style.display = '';
    } else {
      if (noResultsRow) {
        noResultsRow.style.display = 'none';
      }
    }
  }

  function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    filterDrivers();
  }
</script>
