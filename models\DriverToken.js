module.exports = (sequelize, DataTypes) => {
  const DriverToken = sequelize.define('DriverToken', {
    token: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    driverId: { // تعديل هنا
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'deliverypeople', // تأكد اسم الجدول في DB
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    deviceInfo: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'معلومات الجهاز (نوع المتصفح، نظام التشغيل، إلخ) - JSON format',
      get() {
        const value = this.getDataValue('deviceInfo');
        return value ? JSON.parse(value) : null;
      },
      set(value) {
        this.setDataValue('deviceInfo', value ? JSON.stringify(value) : null);
      }
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'حالة التوكن (نشط/غير نشط)'
    },
    lastUsed: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'آخر استخدام للتوكن'
    }
  }, {
    tableName: 'DriverTokens', // أفضل بالجمع
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['token']
      },
      {
        fields: ['driverId']
      },
      {
        fields: ['isActive']
      }
    ]
  });

  DriverToken.associate = function(models) {
    DriverToken.belongsTo(models.DeliveryPerson, {
      foreignKey: 'driverId',
      as: 'driver'
    });
  };

  return DriverToken;
};
