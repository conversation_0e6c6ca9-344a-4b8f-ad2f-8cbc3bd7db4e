# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.*.local

# Logs
logs/
*.log
npm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for uploaded files
uploads/
public/uploads/

# Testing
coverage/
.nyc_output/

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Build output
dist/
build/

# Temporary files
tmp/
temp/

# Database
*.sqlite
*.sqlite3

# TypeScript
*.tsbuildinfo 