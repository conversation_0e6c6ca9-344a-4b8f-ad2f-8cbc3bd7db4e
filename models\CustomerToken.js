// models/CustomerToken.js
module.exports = (sequelize, DataTypes) => {
  const CustomerToken = sequelize.define('CustomerToken', {
    token: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    customerId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Customers',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    deviceInfo: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'معلومات الجهاز (نوع المتصفح، نظام التشغيل، إلخ) - JSON format',
      get() {
        const value = this.getDataValue('deviceInfo');
        return value ? JSON.parse(value) : null;
      },
      set(value) {
        this.setDataValue('deviceInfo', value ? JSON.stringify(value) : null);
      }
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'حالة التوكن (نشط/غير نشط)'
    },
    lastUsed: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'آخر استخدام للتوكن'
    }
  }, {
    tableName: 'CustomerTokens',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['token']
      },
      {
        fields: ['customerId']
      },
      {
        fields: ['isActive']
      }
    ]
  });

  CustomerToken.associate = function(models) {
    CustomerToken.belongsTo(models.Customer, {
      foreignKey: 'customerId',
      as: 'customer'
    });
  };

  return CustomerToken;
};
